import { OrderList } from "../components/Orders/ordersList";
import { useFetchProdcuts } from "../components/hooks/useFetchProducts";
import { useCallback, useEffect, useState } from "react";
import useDebounce from "../components/hooks/useDebounce";
import { useCreateOrderOnERP } from "../components/hooks/useCreateOrderOnERP";
import { useCreateAllOrdersOnERP } from "../components/hooks/useCreateAllOrderOnERP";

const OrderListWrapper = () => {
  const [searchValue, setSearchValue] = useState("");
  const [searchParams, setSearchParams] = useState({ page: 1 });
  const [selectedFilter, setSelectedFilter] = useState("");
  const [selectedOrders, setSelectedOrders] = useState(null);
  const [createErpButtonClicked, setCreateErpButtonClicked] = useState(false);
  const [refreshButtonClicked, setRefreshButtonClicked] = useState(false);
  const [createAllErpButtonClicked, setCreateAllErpButtonClicked] =
    useState(false);
  const debouncedValue = useDebounce(searchValue, 500);
  const [selectedStatus, setSelectedStatus] = useState("ALL");
  const [selectedDate, setSelectedDate] = useState("");
  const [page, setPage] = useState(1);
  const [inputValue, setInputValue] = useState("");

  // const handleSelectedString = useCallback((state) => {
  //   setSelectedString(state);
  // }, []);

  const { isCreated: isAllCreated, isCreating: isAllCreating } =
    useCreateAllOrdersOnERP(
      createAllErpButtonClicked,
      setCreateAllErpButtonClicked,
      inputValue,
      setInputValue
    );

  const { isCreated, isCreating } = useCreateOrderOnERP(
    selectedOrders,
    createErpButtonClicked,
    setCreateErpButtonClicked,
    inputValue,
    setInputValue
  );

  const { data, success, loading } = useFetchProdcuts(
    refreshButtonClicked,
    setRefreshButtonClicked,
    searchParams
  );

  useEffect(() => {
    setSearchParams((prev) => {
      let updatedParams = { ...prev, page };

      // Search
      if (debouncedValue) {
        updatedParams.search = debouncedValue;
      } else {
        delete updatedParams.search;
      }

      // Status
      if (selectedStatus && selectedStatus !== "ALL") {
        updatedParams.status = selectedStatus;
      } else {
        delete updatedParams.status;
      }

      // Date
      if (
        selectedDate?.startsWith("CUSTOM_") ||
        ["TODAY", "LAST7DAYS", "LAST3MONTHS"].includes(selectedDate)
      ) {
        updatedParams.date = selectedDate;
      } else {
        delete updatedParams.date;
      }

      return updatedParams;
    });
  }, [debouncedValue, selectedStatus, selectedDate, page]);
  useEffect(() => {
    if (selectedStatus) {
      setPage(1);
    }
  }, [selectedStatus]);

  useEffect(() => {
    if (isCreated || isAllCreated) {
      setRefreshButtonClicked(true);
    }
  }, [isCreated, isAllCreated]);

  return (
    <div style={{ marginLeft: "2vw", marginRight: "2vw" }}>
      <OrderList
        orders={data?.orders ?? []}
        currentPage={data?.currentPage || 1}
        totalPages={data?.totalPages}
        isCreating={isCreating}
        setSearchValue={setSearchValue}
        selectedFilter={selectedFilter}
        setSelectedFilter={setSelectedFilter}
        setSelectedOrders={setSelectedOrders}
        setCreateErpButtonClicked={setCreateErpButtonClicked}
        refreshButtonClicked={refreshButtonClicked}
        setRefreshButtonClicked={setRefreshButtonClicked}
        selectedStatus={selectedStatus}
        setSelectedStatus={setSelectedStatus}
        selectedDate={selectedDate}
        setSelectedDate={setSelectedDate}
        setCreateAllErpButtonClicked={setCreateAllErpButtonClicked}
        isAllCreating={isAllCreating}
        page={page}
        setPage={setPage}
        loading={loading}
        success={success}
        setInputValue={setInputValue}
        inputValue={inputValue}
        isTableLoading={loading}
      />
    </div>
  );
};

export default OrderListWrapper;
