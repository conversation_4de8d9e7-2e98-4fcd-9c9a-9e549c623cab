import axios from "axios";
import ProductMap from "../../model/erp.productmap.model.js";
import {
  CREATE_LOCATION_SHOPIFY,
  CREATE_PRODUCT_WITH_VARIANTS,
  CREATE_VARIANT,
  GET_SHOPIFY_LOCATIONS,
  INVENTORY_ACTIVATE,
  INVENTORY_SET_QUANTITIES,
  UPDATE_VARIANT,
} from "../../queries/internalQueries.js";
import fetchGraphqlDataShopify from "../../utils/fetchGraphqlDataShopify.js";
import FailedProductLog from "../../model/erp.product.error.model.js";
import { PRODUCTS } from "../../Constants/product.constants.js";
import NewAppError from "../../utils/errorHandlers.js";
import { LOCATION, B2BorB2C } from "../../Constants/erpConstants.js";

export const fetchERPItemsBatch = async (page = 1, pageSize = 50000) => {
  try {
    const url = `${process.env.SAGE_CUSTOM_API_ENDPOINT}/api/ICItem/GetICItems`;
    const params = {
      company: process.env.SAGE_COMPANY,
      APIKey: process.env.SAGE_CUSTOM_API_KEY,
      page_no: page,
      page_size: pageSize,
      Location: LOCATION,
      OptionalFields: "BUDSUBGRP,COORIGIN",
      B2BorB2C: B2BorB2C,
      Active: "True",
    };

    const headers = {
      accept: "*/*",
    };

    const response = await axios.get(url, { params, headers });
    console.log(response?.data?.length, "total products");
    return response.data || [];
  } catch (error) {
    console.error(`Error fetching ERP items on page ${page}:`, error.message);
    return [];
  }
};

export const getExistingLocationsShopify = async () => {
  try {
    const response = await fetchGraphqlDataShopify(GET_SHOPIFY_LOCATIONS, {});
    if (response.error) {
      throw Error(
        `Error when fetching existing locations.Error:${response?.error?.response?.data?.errors}`
      );
    }

    const responseData =
      response?.data?.locations?.edges?.map((location) => {
        return { id: location?.node?.id, name: location?.node?.name };
      }) || [];

    //responseData =[{id:"75220123748",name:"1SR"}]
    return responseData;
  } catch (error) {
    console.log(error?.message, `Error when fetching existing locations`);
    throw new NewAppError("FETCHING_LOCATIONS_ERROR", error?.message, 400);
  }
};

const createLocationOnShopify = async (location) => {
  try {
    const variables = {
      input: { name: location, address: { countryCode: "MY" } },
    };

    const response = await fetchGraphqlDataShopify(
      CREATE_LOCATION_SHOPIFY,
      variables
    );
    if (response.error) {
      throw new NewAppError(
        "SHOPIFY_ERROR",
        `Error when creating location.Error:${response?.error?.response?.data?.errors}`,
        400
      );
    }
    const locationId = response?.data?.locationAdd?.location?.id;

    return {
      id: locationId,
      name: location,
    };
  } catch (error) {
    //Log
    await saveFailedProduct(
      null,
      null,
      "createLocationOnShopify",
      `Error when creating location.Error:${error.message}`
    );
    console.log(error.message, `Error when creating location`);
    return null;
  }
};

const handleNewLocation = async (itemLocation) => {
  try {
    if (!itemLocation) {
      console.log("Invalid itemLocation object: missing Location.");
      return null;
    }
    const locationName = itemLocation?.trim();

    let existingLocations = await getExistingLocationsShopify();

    // Check if the location already exists in Shopfiy, if not create a new locatioin and return the new location detai
    const alreadyExists = existingLocations?.find(
      (shopifyLocation) => shopifyLocation?.name?.trim() === locationName
    );
    if (!alreadyExists) {
      const newLocation = await createLocationOnShopify(locationName);
      // console.log(newLocation, "newLocation");
      if (newLocation) {
        existingLocations.push(newLocation);
      }
    }

    return existingLocations;
  } catch (error) {
    await saveFailedProduct(null, null, "handleNewLocation", {}, error.message);
    console.error("Error in handleNewLocation:", error.message);
    return null;
  }
};

const extractLocationInfo = (item) => {
  const locationData = [];

  // Extract all unique location codes based on keys ending with '_QuantityOnHand'
  const locationCodes = Object.keys(item)
    .filter((key) => key.endsWith("_QuantityOnHand"))
    .map((key) => key.split("_")[0]);

  for (const location of locationCodes) {
    let quantityOnHand = item[`${location}_QuantityOnHand`];
    let quantityAvailable = item[`${location}_QuantityAvailable`];

    quantityOnHand =
      quantityOnHand != null && quantityOnHand >= 0 ? quantityOnHand : 0;
    quantityAvailable =
      quantityAvailable != null && quantityAvailable >= 0
        ? quantityAvailable
        : 0;

    locationData.push({
      location,
      quantityOnHand,
      quantityAvailable,
    });

    //Logic if there is multiple quantities and only want to save have postive greatore than 0 value

    // const isValid =
    //   quantityOnHand !== null &&
    //   quantityOnHand !== undefined &&
    //   quantityOnHand > 0 &&
    //   quantityAvailable !== null &&
    //   quantityAvailable !== undefined &&
    //   quantityAvailable > 0;

    // if (isValid) {
    //   locationData.push({
    //     location,
    //     quantityOnHand,
    //     quantityAvailable,
    //   });
    // }
  }
  return locationData;
};

const createProductWithVariant = async (productData) => {
  try {
    const variantMetafields = [];
    const productMetafields = [];

    if (productData.segment1) {
      productMetafields.push({
        namespace: "custom",
        key: "model",
        type: "single_line_text_field",
        value: productData.segment1,
      });
    }

    if (productData.budgetCategory) {
      productMetafields.push({
        namespace: "custom",
        key: "budget_category",
        type: "single_line_text_field",
        value: productData.budgetCategory,
      });
    }

    if (productData.countryOfOrigin) {
      variantMetafields.push({
        namespace: "custom",
        type: "single_line_text_field",
        key: "countryoforigin",
        value: productData.countryOfOrigin,
      });
    }
    const variables = {
      inventoryPolicy: "DENY",
      input: {
        title: productData?.title,
        productOptions: [
          {
            name: "Color",
            position: 1,
            values: [{ name: productData?.color }],
          },
          {
            name: "Size",
            position: 2,
            values: [{ name: productData?.size }],
          },
        ],
        metafields: productMetafields,
        status: "DRAFT",
        variants: [
          {
            sku: productData?.sku,
            price: String(productData?.itemPriceData),
            compareAtPrice: productData?.itemComparedAtPriceData,
            inventoryItem: {
              tracked: true,
              sku: productData?.sku,
            },
            inventoryQuantities: [
              {
                locationId: productData?.itemLocationId,
                name: "on_hand",
                quantity: productData?.quantityOnHand,
              },
            ],
            metafields: variantMetafields,
            optionValues: [
              {
                optionName: "Color",
                name: productData?.color,
              },
              {
                optionName: "Size",
                name: productData?.size,
              },
            ],
            inventoryPolicy: "DENY",
          },
        ],
      },
    };

    const response = await fetchGraphqlDataShopify(
      CREATE_PRODUCT_WITH_VARIANTS,
      variables
    );
    const result = response.data?.productSet;

    if (result?.userErrors?.length) {
      throw new NewAppError(
        "Shopify_Error",
        `Create product failed: ${result.userErrors
          .map((e) => e.message)
          .join(", ")}`,
        400
      );
    }

    const productId = result.product.id;
    const variant = result?.product?.variants?.nodes[0];

    await activateInventoryItemAtLocation(
      variant?.inventoryItem?.id,
      productData?.itemLocationId
    );

    // const vdata = await createProductVariant(productId, productData);
    // console.log(vdata, "vdatata hetre");
    // Create ProductMap only after both product and variant are created successfully
    const productMap = await ProductMap.findOneAndUpdate(
      { segment1: productData?.segment1?.trim() },
      {
        $set: {
          segment1: productData?.segment1?.trim(),
          productId,
        },
        $addToSet: {
          variants: {
            sku: variant.sku,
            variantId: variant.id,
            price: variant.price,
            location: productData.itemLocation?.trim(),
            locationName: productData.itemLocationId?.trim(),
          },
        },
      },
      { upsert: true, new: true }
    );

    await productMap.save();
    return { productId, variant };
  } catch (error) {
    console.error("Error creating product:", error.message);
    throw error;
  }
};

const activateInventoryItemAtLocation = async (inventoryItemId, locationId) => {
  try {
    const gidInventoryItemId = inventoryItemId.startsWith("gid://")
      ? inventoryItemId
      : `gid://shopify/InventoryItem/${inventoryItemId}`;
    const gidLocationId = locationId.startsWith("gid://")
      ? locationId
      : `gid://shopify/Location/${locationId}`;

    const response = await fetchGraphqlDataShopify(INVENTORY_ACTIVATE, {
      inventoryItemId: gidInventoryItemId,
      locationId: gidLocationId,
    });

    const errors = response?.data?.inventoryActivate?.userErrors;
    if (errors?.length) {
      throw new Error(
        `Inventory activation failed: ${errors
          .map((e) => e.message)
          .join(", ")}`
      );
    }
  } catch (err) {
    console.error("Error activating inventory:", err.message);
    throw new NewAppError("SHOPIFY_ERROR", err?.message, 400);
  }
};

export const setInventoryLevel = async (
  inventoryItemId,
  locationId,
  availableQuantity
) => {
  try {
    const gidInventoryItemId = inventoryItemId.startsWith("gid://")
      ? inventoryItemId
      : `gid://shopify/InventoryItem/${inventoryItemId}`;
    // const gidInventoryItemId = `gid://shopify/InventoryItem/${inventoryItemId}`;
    const gidLocationId = locationId.startsWith("gid://")
      ? locationId
      : `gid://shopify/Location/${locationId}`;

    const response = await fetchGraphqlDataShopify(INVENTORY_SET_QUANTITIES, {
      input: {
        name: "available",
        reason: "correction",
        ignoreCompareQuantity: true,
        quantities: [
          {
            inventoryItemId: gidInventoryItemId,
            locationId: gidLocationId,
            quantity: availableQuantity,
          },
        ],
      },
    });

    if (response.data?.inventorySetQuantities?.userErrors?.length > 0) {
      const errors = response.data?.inventorySetQuantities?.userErrors;
      throw new NewAppError(
        "SHOPIFY_ERROR",
        `Error setting inventory level: ${errors
          .map((e) => e.message)
          .join(", ")}`,
        400
      );
    }
    return true;
  } catch (error) {
    console.error("Error setting inventory level:", error);
    throw new NewAppError("SHOPIFY_ERROR", error?.message, 400);
  }
};

const addOrUpdateVariant = async (productId, variantData, existingVariants) => {
  try {
    const existingVariant = existingVariants.find(
      (v) => v.sku === variantData.sku
    );

    if (existingVariant) {
      console.log(
        `Updating existing variant with ID: ${existingVariant.variantId}`
      );
      await updateVariant(productId, existingVariant?.variantId, variantData);
    } else {
      console.log("Creating new variant");
      await createVariant(productId, variantData);
    }
  } catch (error) {
    console.log("Error when adding Or Updating Variant", error?.message);
    throw new NewAppError("SHOPIFY_ERROR", error?.message, 400);
  }
};

const createVariant = async (productId, productData) => {
  const variantMetafields = [];

  if (productData?.countryOfOrigin) {
    variantMetafields.push({
      namespace: "custom",
      type: "single_line_text_field",
      key: "countryoforigin",
      value: productData.countryOfOrigin,
    });
  }
  try {
    const variables = {
      productId,
      variantsInput: [
        {
          price: productData?.itemPriceData?.toString() || "10.00",
          inventoryItem: {
            sku: productData.sku?.trim(),
          },
          metafields: variantMetafields,
          optionValues: [
            { name: productData.color?.trim(), optionName: "Color" },
            { name: productData.size?.trim(), optionName: "Size" },
          ],
        },
      ],
    };

    const response = await fetchGraphqlDataShopify(CREATE_VARIANT, variables);
    const result = response.data?.productVariantsBulkCreate;

    if (result?.userErrors?.length) {
      throw new NewAppError(
        "SHOPIFY_ERROR",
        `Create variant failed: ${result.userErrors
          .map((e) => e.message)
          .join(", ")}`,
        400
      );
    }

    // Fix: Properly check if we have a valid variant with an ID
    const variant = result?.productVariants?.[0];
    if (!variant || !variant.id) {
      throw new Error("Failed to get variant ID from creation response");
    }

    // Save mapping
    await ProductMap.findOneAndUpdate(
      { segment1: productData?.segment1?.trim() },
      {
        $set: {
          segment1: productData?.segment1?.trim(),
          productId,
        },
        $addToSet: {
          variants: {
            sku: productData?.sku?.trim(),
            variantId: variant.id, // Now we're sure this exists
            location: productData?.itemLocationId?.trim(),
            locationName: productData?.itemLocation?.trim(),
          },
        },
      },
      { upsert: true, new: true }
    );

    const inventoryItemId = variant.inventoryItem.id;

    await activateInventoryItemAtLocation(
      inventoryItemId,
      productData.itemLocationId?.trim()
    );

    await setInventoryLevel(
      inventoryItemId,
      productData.itemLocationId?.trim(),
      productData.quantityOnHand
    );
    return variant; // Return the variant for potential further use
  } catch (error) {
    console.error("Error creating variant:", error.message);
    throw new NewAppError("SHOPIFY_ERRORS", error.message, 500);
  }
};

const updateVariant = async (productId, variantId, updatedData) => {
  try {
    const variantMetafields = [];

    if (updatedData.countryOfOrigin) {
      variantMetafields.push({
        namespace: "custom",
        type: "single_line_text_field",
        key: "countryoforigin",
        value: updatedData.countryOfOrigin,
      });
    }

    const variables = {
      productId,
      variantsInput: [
        {
          id: variantId,
          price: updatedData?.itemPriceData?.toString() || "00.00",
          metafields: variantMetafields,
          optionValues: [
            { name: updatedData.color?.trim(), optionName: "Color" },
            { name: updatedData.size?.trim(), optionName: "Size" },
          ],
        },
      ],
    };

    // Add the API call here
    const response = await fetchGraphqlDataShopify(UPDATE_VARIANT, variables);

    if (response.data?.productVariantsBulkUpdate?.userErrors?.length) {
      throw new Error(
        "SHOPIFY_ERRORS",
        `Update variant failed: ${response.data?.productVariantsBulkUpdate?.userErrors
          .map((e) => e.message)
          .join(", ")}`,
        400
      );
    }
    const updatedVariant =
      response.data?.productVariantsBulkUpdate?.productVariants[0];

    const inventoryItemId = updatedVariant?.inventoryItem?.id; // Retrieve inventory_item_id from REST API response
    const inventoryQuantity = updatedData.quantityOnHand;

    await activateInventoryItemAtLocation(
      inventoryItemId,
      updatedData?.itemLocationId
    );

    await setInventoryLevel(
      inventoryItemId,
      updatedData?.itemLocationId,
      inventoryQuantity
    );

    return response;
  } catch (error) {
    console.log("Error when updating variant:", error?.message);
    throw error;
  }
};

const saveFailedProduct = async (
  segment1,
  sku,
  event,
  data = {},
  errorMessage
) => {
  try {
    const failedProduct = new FailedProductLog({
      segment1,
      sku,
      event,
      data,
      errorMessage,
    });
    await failedProduct.save();
    console.log(`Saved failed product with segment1: ${segment1}`);
  } catch (error) {
    throw new NewAppError("SERVER ERROR", error.message, 500);
  }
};

export const syncERPProducts = async () => {
  let page = 1;
  const pageSize = 50000;
  let processedProducts = 0;
  let totalProducts = 0;

  while (true) {
    const items = await fetchERPItemsBatch(page, pageSize);
    // const items = PRODUCTS;
    const currentTime = new Date();
    const istTime = currentTime.toLocaleString("en-IN", {
      timeZone: "Asia/Kolkata",
    });
    console.log("Start Time in IST: ", istTime);
    if (!items.length) {
      console.log("No items returned. Ending sync.");
      console.log(`Total products processed: ${processedProducts}`);
      break;
    }

    if (page === 1) {
      totalProducts = items.length;
      console.log(`Total products to process: ${totalProducts}`);
    }

    for (const item of items) {
      try {
        const segment1 = item.ItemNumber?.split("-")[0]?.trim();
        const existingMapping = await ProductMap.findOne({
          segment1: segment1,
        });

        const result = extractLocationInfo(item);
        if (!result || !result[0]) {
          console.log(
            `No location info found for item: ${item.ItemNumber}. Skipping...`
          );
          processedProducts++;
          console.log(
            `Processed ${processedProducts} out of ${totalProducts} products`
          );
          continue;
        }
        const { location, quantityOnHand, quantityAvailable } = result[0];
        if (!location) continue;

        const locations = await handleNewLocation(location);

        const itemLocationId = locations.find(
          (loc) => loc.name === location
        )?.id;

        if (!itemLocationId) {
          console.log(
            `Location not found for item: ${item.ItemNumber}. Skipping...`
          );
          processedProducts++;
          console.log(
            `Processed ${processedProducts} out of ${totalProducts} products`
          );
          continue;
        }

        if (existingMapping) {
          console.log(
            `Product exists for segment1: ${segment1}. Updating variant...`
          );

          await addOrUpdateVariant(
            existingMapping.productId,
            {
              title: item.Model,
              size: item.Size?.trim(),
              color: item.Color?.trim(),
              sku: item.UnformattedItemNumber?.trim(),
              itemLocation: location,
              itemLocationId: itemLocationId,
              quantityAvailable: quantityAvailable || 0,
              quantityOnHand: quantityOnHand || 0,
              itemPriceData: item.BasePrice || 10,
              itemComparedAtPriceData: item.BasePrice || 10,
              countryOfOrigin: item.COORIGIN?.trim(),
              budgetCategory: item.BUDSUBGRP?.trim(),
            },
            existingMapping.variants
          );
        } else {
          console.log(
            `No product found for segment1: ${segment1}. Creating new product...`
          );
          await createProductWithVariant({
            title: item.Model,
            segment1: segment1,
            size: item.Size?.trim(),
            color: item.Color?.trim(),
            sku: item.UnformattedItemNumber?.trim(),
            itemLocation: location?.trim(),
            itemLocationId: itemLocationId,
            quantityAvailable: quantityAvailable || 0,
            quantityOnHand: quantityOnHand || 0,
            itemPriceData: item.BasePrice || 10,
            itemComparedAtPriceData: item.BasePrice || 10,
            countryOfOrigin: item.COORIGIN?.trim(),
            budgetCategory: item.BUDSUBGRP?.trim(),
          });
        }

        processedProducts++;
        console.log(
          `Processed ${processedProducts} out of ${totalProducts} products`
        );
      } catch (err) {
        console.error(
          `Error syncing product ${item.ItemNumber?.split("-")[0]?.trim()}:`,
          err.message
        );
        await saveFailedProduct(
          item.ItemNumber?.split("-")[0]?.trim(),
          item.UnformattedItemNumber,
          "syncERPProducts",
          { error: err.message },
          err.message
        );
        continue;
      }
    }

    if (items.length < pageSize) {
      console.log("Final batch processed.");
      const currentTime = new Date();
      const endTime = currentTime.toLocaleString("en-IN", {
        timeZone: "Asia/Kolkata",
      });
      console.log("End Time in IST: ", endTime);
      console.log(`Total products processed: ${processedProducts}`);
      break;
    }

    page += 1;
  }

  return {
    totalProducts,
    processedProducts,
    completedPages: page,
  };
};
