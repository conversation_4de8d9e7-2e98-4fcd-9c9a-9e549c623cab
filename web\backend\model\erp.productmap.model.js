import mongoose from "mongoose";

const VariantSchema = new mongoose.Schema({
  sku: {
    type: String,
    required: true,
    unique: true,
  },
  variantId: {
    type: String,
    required: true,
  },
});

const ProductMapSchema = new mongoose.Schema(
  {
    segment1: {
      type: String,
      required: true,
      unique: true,
    },
    productId: {
      type: String,
      required: true,
    },
    variants: [VariantSchema],
  },
  { timestamps: true }
);

const ProductMap = mongoose.model("ProductMap", ProductMapSchema);
export default ProductMap;
