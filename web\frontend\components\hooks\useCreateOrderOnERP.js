import { useEffect, useState } from "react";
import { CREATE_ORDER_ON_ERP } from "../../Constant/orders";
import { useAppBridge } from "@shopify/app-bridge-react";

export const useCreateOrderOnERP = (
  selectedOrders,
  createErpButtonClicked,
  setCreateErpButtonClicked,
  comment,
  setInputValue
) => {
  const shopify = useAppBridge();
  const [isCreating, setIsCreating] = useState(false);
  const [isCreated, setIsCreated] = useState(false);
  const [errorInCreating, setErrorInCreating] = useState(false);

  useEffect(() => {
    const createOrder = async () => {
      try {
        setIsCreated(false);
        setIsCreating(true);
        setErrorInCreating(false);

        const response = await fetch(CREATE_ORDER_ON_ERP, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },

          body: JSON.stringify({ orderIds: selectedOrders, comment }),
        });

        setInputValue("");

        if (!response.ok) {
          shopify.toast.show(
            "Error in creating the orders on the ERP. Please try again later.",
            {
              isError: true,
              duration: 60000,
            }
          );

          return;
        }

        const data = await response.json();

        if (data?.data?.data?.failedOrders.length > 0) {
          shopify.toast.show(
            `${data?.data?.data?.failedOrders[0].error} ` || "Error occured while pushing to ERP.",
            {
              isError: true,
              duration: 60000,
            }
          );
          setErrorInCreating(true);
        } else {
          shopify.toast.show("Successfully pushed to the ERP.", {
            duration: 60000,
            isError: false,
          });

          setIsCreated(true);
        }
      } catch (err) {
        setErrorInCreating(true);

        shopify.toast.show(
          "Something went wrong while processing the orders. Please try again later.",
          {
            duration: 60000,
            isError: true,
          }
        );
      } finally {
        setCreateErpButtonClicked(false);
        setIsCreating(false);
      }
    };

    if (selectedOrders?.length && createErpButtonClicked) {
      createOrder();
    }
  }, [selectedOrders, createErpButtonClicked]);

  return { isCreated, isCreating, errorInCreating };
};
