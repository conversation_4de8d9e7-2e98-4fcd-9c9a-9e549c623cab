import Order from "../../model/order.js";

const saveFulfillmentRecord = async (body) => {
  try {
    const formattedLineItems = body.line_items.map((item) => {
      // Finding the specific property values by name
      const productType =
        item.properties.find((p) => p.name === "_product_type")?.value ||
        "NOT_AVAILABLE";

      const productPriceStr =
        item.properties.find((p) => p.name === "_string_product_price")
          ?.value || "0";

      const priceNum = item.price ? parseFloat(item.price) : 0;
      const discountNum = item.total_discount
        ? parseFloat(item.total_discount)
        : 0;
      const productPriceNum = parseFloat(productPriceStr);

      const finalPrice = (priceNum - discountNum + productPriceNum).toFixed(2);

      return {
        id: item.id,
        quantity: item.quantity,
        sku: item.sku,
        refundedQuantity: 0,
        remainingQuantity: item.quantity,
        title: item.title,
        price: finalPrice.toString(),
        variantId: item.variant_id,
        stringPrice: productPriceStr,
        discount: item.total_discount || "0",
        productType,
      };
    });

    const doc = new Order({
      orderName: body.name,
      customerName: body.customer
        ? body.customer.first_name + " " + body.customer.last_name
        : "N/A",
      totalPrice: body.total_price,
      paymentStatus: body.financial_status,
      fulfillment_id: body.id,
      orderId: body.id,
      lineItems: formattedLineItems,
      shopify_updated_At: body.updated_at,
      shopify_created_At: body.created_at,
    });

    await doc.save();
  } catch (err) {
    console.log(
      "Error occurred in creating the fulfillment in the Database",
      err
    );
  }
};
// const matchTheLineItemIds = async (existingLineItems, currentLineItems) => {
//   try {
//     const result = existingLineItems.filter(
//       (item) => !currentLineItems.includes(item.id)
//     );
//     return result;
//   } catch (err) {
//     console.log(" Error in matching the line item ids", err);
//   }
// };

// const checkTheStatus = async (fulfillmentId) => {
//   try {
//     const fulfillment = await checkFulfillments(fulfillmentId);
//     return fulfillment.status;
//   } catch (err) {
//     console.log(" Error in checking the status of the fulfillment", err);
//   }
// };

const checkFulfillments = async (fulfillmentId) => {
  try {
    const fulfillment = await Order.findOne({ fulfillment_id: fulfillmentId });
    return fulfillment;
  } catch (err) {
    console.log("Error occured while checking the Fulfillments", err);
  }
};

export const createFulFillmentService = async (body) => {
  try {
    const isFulFillmentExist = await checkFulfillments(body.id);

    if (!isFulFillmentExist) {
      await saveFulfillmentRecord(body);
    } else {
      return;
    }
  } catch (err) {
    console.log(" Error in creating the fulfillments", err);
  }
};
