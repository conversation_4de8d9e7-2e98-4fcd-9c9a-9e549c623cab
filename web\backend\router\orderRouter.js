import express from "express";
import {
  getAllOrders,
  getDataFromDB,
  undoOrder,
} from "../controller/orderController.js";
import {
  createAllPendingOrdersOnErpController,
  createERPOrderController,
} from "../controller/sageController.js";

const router = express.Router();

router.route("/fulfilled-orders").get(getAllOrders);
router.route("/").get(getDataFromDB);
router.route("/erp/create").post(createERPOrderController);
router.route("/erp/create/all").post(createAllPendingOrdersOnErpController);
router.route("/erp/undo").get(undoOrder);

export default router;
