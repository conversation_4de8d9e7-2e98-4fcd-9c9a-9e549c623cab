import Order from "../../model/order.js";
import moment from "moment";

const formattedSearchQuery = (queryParams) => {
  const formattedQuery = Object.entries(queryParams).reduce(
    (acc, [key, value]) => {
      if (key === "search" && value) {
        acc["$or"] = [
          { orderName: { $regex: value, $options: "i" } },
          { orderId: { $regex: value, $options: "i" } },
        ];
      } else if (key !== "status") {
        acc[key] = value;
      }
      return acc;
    },
    {}
  );
  return formattedQuery;
};

const paginateResults = (orders, page, limit) => {
  const totalOrders = orders.length;
  const paginatedOrders = orders.slice((page - 1) * limit, page * limit);
  const totalPages = Math.ceil(totalOrders / limit);

  return {
    orders: paginatedOrders,
    totalPages,
    currentPage: page,
  };
};

const getDateRange = (status) => {
  switch (status) {
    case "LAST7DAYS":
      return {
        start: moment.utc().subtract(7, "days").startOf("day").toDate(),
        end: moment.utc().endOf("day").toDate(),
      };
    case "TODAY":
      return {
        start: moment.utc().startOf("day").toDate(),
        end: moment.utc().endOf("day").toDate(),
      };
    case "LAST3MONTHS":
      return {
        start: moment.utc().subtract(3, "months").startOf("day").toDate(),
        end: moment.utc().endOf("day").toDate(),
      };
    default:
      return null;
  }
};


export const getALLOrdersService = async (req) => {
  const queryParams = req.query;
  const limit = Number(queryParams?.limit) || 50;
  const page = Number(queryParams?.page) || 1;
  const { page: _, limit: __, search, status, date, ...rest } = queryParams;

  const queryConditions = {};

  if (status === "REFUNDED") {
    // Refunded handled separately
    const refundedOrders = await Order.find({ "lineItems.refunded": true });
    const filtered = refundedOrders.map((order) => ({
      ...order.toObject(),
      lineItems: order.lineItems.filter((item) => item.refunded),
    }));
    return paginateResults(filtered, page, limit);
  }

  if (status) {
    queryConditions.status = status;
  }

  // ✅ Handle date filter
  if (date?.startsWith("CUSTOM_")) {
    const dateRangeStr = date.replace("CUSTOM_", "");
    const [fromStr, , toStr] = dateRangeStr.split("_");

    const fromDate = moment.utc(fromStr, "YYYY-MM-DD").startOf("day").toDate();
    const toDate = moment.utc(toStr, "YYYY-MM-DD").endOf("day").toDate();

    queryConditions.createdAt = { $gte: fromDate, $lte: toDate };
  } else if (["TODAY", "LAST7DAYS", "LAST3MONTHS"].includes(date)) {
    const dateRange = getDateRange(date);
    queryConditions.createdAt = {
      $gte: dateRange.start,
      $lte: dateRange.end,
    };
  }

  if (search) {
    queryConditions.$or = [
      { orderName: { $regex: search, $options: "i" } },
      { orderNumber: { $regex: search, $options: "i" } },
      { customerName: { $regex: search, $options: "i" } },
    ];
  }

  // Final fetch
  const orders = await Order.find(queryConditions).sort({ createdAt: -1 });

  return paginateResults(orders, page, limit);
};
