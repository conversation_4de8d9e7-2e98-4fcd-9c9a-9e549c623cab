# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "24a245f51ad7ae90197d4cbe9ff7a2d2"
name = "Sunrise-b2c-app-latest"
handle = "sunrise-b2c-app-latest"
application_url = "https://qixpn5a62j.execute-api.ap-southeast-1.amazonaws.com/"
embedded = true

[build]
automatically_update_urls_on_dev = true
dev_store_url = "shiva-internation.myshopify.com"
include_config_on_deploy = true

[webhooks]
api_version = "2025-01"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "write_orders,read_fulfillments,read_inventory,read_locations,read_orders,write_customers,write_inventory,write_locations,write_products"

[auth]
redirect_urls = [
  "https://qixpn5a62j.execute-api.ap-southeast-1.amazonaws.com/auth/callback",
  "https://qixpn5a62j.execute-api.ap-southeast-1.amazonaws.com/auth/shopify/callback",
  "https://qixpn5a62j.execute-api.ap-southeast-1.amazonaws.com/api/auth/callback"
]

[pos]
embedded = false
