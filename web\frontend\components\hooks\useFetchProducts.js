import { useAppBridge } from "@shopify/app-bridge-react";
import { useEffect, useState } from "react";
import { formattedUrl } from "../../utils/orders";

export const useFetchProdcuts = (
  refreshButtonClicked,
  setRefreshButtonClicked,
  searchParams = {}
) => {
  const shopify = useAppBridge();
  const [data, setData] = useState([]);
  const [error, setError] = useState(false);
  const [success, setSuccess] = useState(false);
  const [loading, setLoading] = useState(false);

  const formattedParams =
    Object.keys(searchParams).length && formattedUrl(searchParams);

  const url = !Object.keys(searchParams).length
    ? "/api/order/fulfilled-orders?limit=50"
    : `/api/order/fulfilled-orders?${formattedParams}&limit=50`;

  useEffect(() => {
    const getProducts = async () => {
      try {
        setSuccess(false);
        shopify.loading(true);
        setError(false);

        const response = await fetch(url, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (!response.ok) {
          throw new Error("Failed to fetch orders");
        }

        const data = await response.json();
        setData(data?.data?.data);
        setSuccess(true);

        if (!Object.keys(searchParams).length) {
          shopify.toast.show("Orders fetched Successfully!", {
            duration: 2000,
          });
        }
      } catch (error) {
        console.error("Error fetching orders:", error);
        setError(true);
        shopify.toast.show("Something went wrong while fetching the order", {
          duration: 60000,
          isError: true,
        });
      } finally {
        setLoading(false);
        shopify.loading(false);
        setSuccess(false);
        setRefreshButtonClicked(false);
      }
    };

    if (refreshButtonClicked || Object.keys(searchParams).length > 0) {
      getProducts();
    }
  }, [refreshButtonClicked, searchParams, url]);

  return { data, error, loading, success };
};
