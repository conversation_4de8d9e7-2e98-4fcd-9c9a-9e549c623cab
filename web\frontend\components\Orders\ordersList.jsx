import {
  IndexTable,
  Card,
  Text,
  Badge,
  Button,
  ChoiceList,
  useIndexResourceState,
  useBreakpoints,
  IndexFilters,
  Modal,
  Spinner,
} from "@shopify/polaris";
import { useCallback, useEffect, useState, useRef } from "react";
import { useSetIndexFiltersMode } from "@shopify/polaris";
import { useUndoOrder } from "../hooks/useUndoOrder";

export const OrderList = (props) => {
  const {
    orders,
    currentPage,
    totalPages,
    setSearchValue,
    setSelectedFilter,
    selectedFilter,
    setSelectedOrders,
    setCreateErpButtonClicked,
    isCreating,
    refreshButtonClicked,
    setRefreshButtonClicked,
    selectedStatus,
    setSelectedStatus,
    selectedDate,
    setSelectedDate,
    setCreateAllErpButtonClicked,
    isAllCreating,
    page,
    setPage,
    loading,
    success,
    setInputValue,
    inputValue,
    isTableLoading,
  } = props;

  let rowMarkup;
  const [search, setSearch] = useState("");
  const [limit, setLimit] = useState(25);
  const [hasNext, setHasNext] = useState();
  const { mode, setMode } = useSetIndexFiltersMode();
  const [selected, setSelected] = useState(0);
  const [itemStrings, setItemStrings] = useState([
    "All",
    "Pushed",
    "Pending",
    "Refunded",
  ]);
  const [loadingOrders, setLoadingOrders] = useState([]);

  const [customDateFrom, setCustomDateFrom] = useState("");
  const [customDateTo, setCustomDateTo] = useState("");
  const [queryValue, setQueryValue] = useState("");
  const [selectedDateFilter, setSelectedDateFilter] = useState(null);

  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [showSelectedConfirmDialog, setShowSelectedConfirmDialog] =
    useState(false);
  const [showUndoConfirmDialog, setShowUndoConfirmDialog] = useState(false);
  const [currentUndoOrderName, setCurrentUndoOrderName] = useState(null);

  const handleFulfilmentFilterRemove = useCallback(() => {
    setSelectedFilter([]);
    setSelectedStatus("ALL");
  }, []);

  const handleDateFilterRemove = useCallback(() => {
    setSelectedDateFilter([]);
    setCustomDateFrom("");
    setCustomDateTo("");
    setSelectedDate("");
  }, []);

  const storeName = import.meta.env.VITE_STORE_NAME;

  const handleDateFilterChange = useCallback(
    (value) => {
      const val = value[0];
      setSelectedDateFilter(val);

      if (val === "today") {
        setSelectedDate("TODAY");
      } else if (val === "last7days") {
        setSelectedDate("LAST7DAYS");
      } else if (val === "last3months") {
        setSelectedDate("LAST3MONTHS");
      } else if (val === "custom" && customDateFrom && customDateTo) {
        const formatted = `CUSTOM_${customDateFrom}_TO_${customDateTo}`;
        setSelectedDate(formatted);
      }
    },
    [selectedDate]
  );

  useEffect(() => {
    if (selectedDateFilter === "custom" && customDateFrom && customDateTo) {
      const formatted = `CUSTOM_${customDateFrom}_TO_${customDateTo}`;
      setSelectedDate(formatted);
    }
  }, [customDateFrom, customDateTo, selectedDateFilter, selectedDate]);

  const appliedFilters = [];

  if (selectedFilter && selectedFilter.length > 0) {
    const key = "status";
    appliedFilters.push({
      key,
      label: disambiguateLabel(key, selectedFilter[0]),
      onRemove: handleFulfilmentFilterRemove,
    });
  }

  if (selectedDateFilter) {
    appliedFilters.push({
      key: "date",
      label:
        selectedDateFilter === "custom"
          ? `Date: ${customDateFrom} to ${customDateTo}`
          : `Date: ${selectedDateFilter}`,
      onRemove: handleDateFilterRemove,
    });
  }

  const {
    selectedResources,
    allResourcesSelected,
    handleSelectionChange,
    clearSelection,
  } = useIndexResourceState(orders, {
    resourceIDResolver: (row) => row._id,
    resourceFilter: (row) => {
      return row.status === "PENDING";
    },
  });

  useEffect(() => {
    if (success) {
      clearSelection();
    }
  }, [success]);

  useEffect(() => {
    clearSelection();
  }, [selectedStatus]);

  const resourceName = {
    singular: "order",
    plural: "orders",
  };

  const tabs = itemStrings.map((item, index) => ({
    content: item,
    index,
    onAction: () => {
      setSelectedStatus(item.toUpperCase());
    },
    id: `${item}-${index}`,
    isLocked: index === 0,
    actions: [],
    selected: item.toUpperCase() === selectedStatus,
  }));

  const handleFilterChange = useCallback(
    (value) => {
      setSelectedFilter(value);
      const filterValue = value[0]?.toUpperCase() || "ALL";
      setSelectedStatus(filterValue);
    },
    [selectedStatus]
  );

  const filters = [
    {
      key: "status",
      label: "Fulfillment Status",
      filter: (
        <ChoiceList
          title="Fulfillment Status"
          titleHidden
          choices={[
            { label: "Pushed", value: "PUSHED" },
            { label: "Pending", value: "PENDING" },
          ]}
          selected={selectedFilter || []}
          onChange={handleFilterChange}
          allowMultiple={false}
        />
      ),
      shortcut: true,
    },
    {
      key: "date",
      label: "Date",
      filter: (
        <>
          <ChoiceList
            title="Date"
            titleHidden
            choices={[
              { label: "Today", value: "today" },
              { label: "Last 7 Days", value: "last7days" },
              { label: "Last 3 Months", value: "last3months" },
              { label: "Custom", value: "custom" },
            ]}
            selected={selectedDateFilter ? [selectedDateFilter] : []}
            onChange={handleDateFilterChange}
          />
          {selectedDateFilter === "custom" && (
            <div style={{ marginTop: "10px", display: "flex", gap: "20px" }}>
              <div style={{ display: "flex", flexDirection: "column" }}>
                <label style={{ marginBottom: "4px" }}>From</label>
                <input
                  type="date"
                  value={customDateFrom}
                  onChange={(e) => setCustomDateFrom(e.target.value)}
                  placeholder="From"
                />
              </div>
              <div style={{ display: "flex", flexDirection: "column" }}>
                <label style={{ marginBottom: "4px" }}>To</label>
                <input
                  type="date"
                  value={customDateTo}
                  onChange={(e) => setCustomDateTo(e.target.value)}
                  placeholder="To"
                />
              </div>
            </div>
          )}
        </>
      ),
      shortcut: true,
    },
  ];

  const onHandleCancel = () => {
    setQueryValue("");
    setSearchValue("");
    setSearch("");
  };

  const handleClearFilter = useCallback(() => {
    handleFulfilmentFilterRemove();
    handleDateFilterRemove();
  }, [handleFulfilmentFilterRemove, handleDateFilterRemove]);

  const handleFiltersQueryChange = (value) => {
    setQueryValue(value);
    setSearchValue(value);
    setSearch(value);
  };

  const handleQueryValueRemove = useCallback(() => {
    setQueryValue("");
    setSearchValue("");
    setSearch("");
  }, []);

  const handlePushAllClick = () => {
    setShowConfirmDialog(true);
  };

  const handleConfirmPushAll = () => {
    setShowConfirmDialog(false);
    setCreateAllErpButtonClicked(true);
  };

  const handleCancelPushAll = () => {
    setShowConfirmDialog(false);
    setInputValue("");
  };

  const handlePushSelectedClick = () => {
    setShowSelectedConfirmDialog(true);
  };

  const handleConfirmPushSelected = () => {
    setShowSelectedConfirmDialog(false);
    setCreateErpButtonClicked(true);
    setSelectedOrders(selectedResources);
    clearSelection();
  };

  const handleCancelPushSelected = () => {
    setShowSelectedConfirmDialog(false);
    setInputValue("");
  };

  const handleUndoClick = (orderId) => {
    setCurrentUndoOrderName(orderId);
    setShowUndoConfirmDialog(true);
  };

  const handleConfirmUndo = () => {
    setShowUndoConfirmDialog(false);
    useUndoOrder(
      currentUndoOrderName,
      setLoadingOrders,
      setRefreshButtonClicked
    );
    setCurrentUndoOrderName(null);
  };

  const handleCancelUndo = () => {
    setShowUndoConfirmDialog(false);
    setCurrentUndoOrderName(null);
  };

  const filteredOrders = orders.filter((order) => {
    const searchLower = search.toLowerCase();
    return (
      order.orderName?.toLowerCase().includes(searchLower) ||
      order.customerName?.toLowerCase().includes(searchLower) ||
      order.orderNumber?.toLowerCase().includes(searchLower)
    );
  });

  if (selectedStatus === "REFUNDED") {
    rowMarkup = filteredOrders.flatMap(
      (
        {
          _id,
          orderName,
          orderId,
          status,
          shopify_updated_At,
          paymentStatus,
          customerName,
          lineItems,
          orderNumber,
        },
        orderIndex
      ) => {
        function formatDate(dateString) {
          const date = new Date(dateString);
          const day = String(date.getDate()).padStart(2, "0");
          const month = String(date.getMonth() + 1).padStart(2, "0");
          const year = String(date.getFullYear()).slice(2);
          return `${day}-${month}-${year}`;
        }

        return lineItems.map((item, lineIndex) => (
          <IndexTable.Row
            id={`${_id}-${item.id}`}
            key={`${_id}-${item.id}`}
            selected={selectedResources.includes(_id)}
            position={orderIndex * 100 + lineIndex}
            disabled={true}
          >
            <IndexTable.Cell>
              <Text variant="bodyMd" fontWeight="bold" as="span">
                {orderName}
              </Text>
            </IndexTable.Cell>
            <IndexTable.Cell>{formatDate(shopify_updated_At)}</IndexTable.Cell>
            <IndexTable.Cell>{customerName}</IndexTable.Cell>
            <IndexTable.Cell>{item.price}</IndexTable.Cell>
            <IndexTable.Cell>
              <Badge
                tone={paymentStatus === "paid" ? "success" : "warning"}
                style={{ marginLeft: "4px" }}
              >
                {paymentStatus?.toUpperCase() || "UNKNOWN"}
              </Badge>
            </IndexTable.Cell>
            <IndexTable.Cell>{item.quantity}</IndexTable.Cell>
            <IndexTable.Cell>
              <Badge tone={"default"}>REFUNDED</Badge>
            </IndexTable.Cell>
            <IndexTable.Cell>
              <div style={{ textAlign: "center", width: "100%" }}>
                {orderNumber || "-"}
              </div>
            </IndexTable.Cell>

            <IndexTable.Cell>
              <Button
                plain
                onClick={(e) => {
                  e.stopPropagation();
                  window.open(
                    `https://admin.shopify.com/store/${storeName}/orders/${orderId}`
                  );
                }}
              >
                View Order
              </Button>
            </IndexTable.Cell>
          </IndexTable.Row>
        ));
      }
    );
  } else {
    rowMarkup = filteredOrders.map(
      (
        {
          _id,
          orderName,
          orderId,
          status,
          lineItems,
          shopify_updated_At,
          paymentStatus,
          customerName,
          refunds,
          orderNumber,
        },
        index
      ) => {
        const updatedLineItems = lineItems.map((item) => {
          const refund = refunds?.find(
            (r) => r.line_item_id === parseInt(item.id)
          );
          const refundedQty = refund ? refund.quantity : 0;
          const remainingQty = item.quantity - refundedQty;
          const refundedAmount = refund ? refund.subtotal : 0;

          return {
            ...item,
            refundedQuantity: refundedQty,
            remainingQuantity: remainingQty,
            refundedAmount: refundedAmount,
          };
        });

        const totalQuantity = updatedLineItems.reduce(
          (acc, item) => acc + (item.remainingQuantity || 0),
          0
        );
        const adjustedTotalPrice = updatedLineItems
          .reduce((acc, item) => {
            if (item.remainingQuantity > 0) {
              return acc + parseFloat(item.price) * item.remainingQuantity;
            }
            return acc;
          }, 0)
          .toFixed(2);

        if (totalQuantity === 0) return null;

        function formatDate(dateString) {
          const date = new Date(dateString);
          const day = String(date.getDate()).padStart(2, "0");
          const month = String(date.getMonth() + 1).padStart(2, "0");
          const year = String(date.getFullYear()).slice(2);
          return `${day}-${month}-${year}`;
        }

        return (
          <IndexTable.Row
            id={_id}
            key={_id}
            selected={selectedResources.includes(_id) && status === "PENDING"}
            position={index}
            disabled={status === "PUSHED"}
          >
            <IndexTable.Cell style={{ textAlign: "center", width: "100%" }}>
              <Text variant="bodyMd" fontWeight="bold" as="span">
                {orderName}
              </Text>
            </IndexTable.Cell>
            <IndexTable.Cell>
              <div style={{ textAlign: "center", width: "100%" }}>
                {formatDate(shopify_updated_At)}
              </div>
            </IndexTable.Cell>
            <IndexTable.Cell>
              <div style={{ textAlign: "center", width: "100%" }}>
                {customerName}
              </div>
            </IndexTable.Cell>
            <IndexTable.Cell>
              <div style={{ textAlign: "center", width: "100%" }}>
                {adjustedTotalPrice}
              </div>{" "}
            </IndexTable.Cell>
            <IndexTable.Cell style={{ textAlign: "center", width: "100%" }}>
              <div style={{ textAlign: "center", width: "100%" }}>
                <Badge
                  tone={paymentStatus === "paid" ? "success" : "warning"}
                  style={{ marginLeft: "4px" }}
                >
                  {paymentStatus?.toUpperCase() || "UNKNOWN"}
                </Badge>
              </div>
            </IndexTable.Cell>
            <IndexTable.Cell>
              {" "}
              <div style={{ textAlign: "center", width: "100%" }}>
                {totalQuantity}
              </div>
            </IndexTable.Cell>
            <IndexTable.Cell>
              <div style={{ textAlign: "center", width: "100%" }}>
                <Badge tone={status === "PENDING" ? "critical" : "success"}>
                  {status}
                </Badge>
              </div>
            </IndexTable.Cell>
            <IndexTable.Cell style={{ textAlign: "center", width: "100%" }}>
              <div style={{ textAlign: "center", width: "100%" }}>
                {orderNumber || "-"}
              </div>
            </IndexTable.Cell>

            <IndexTable.Cell>
              <div
                style={{
                  textAlign: "center",
                  width: "100%",
                  display: "flex",
                  justifyContent: "space-around",
                }}
              >
                <Button
                  plain
                  disabled={status !== "PUSHED"}
                  onClick={(e) => {
                    e.stopPropagation();
                    handleUndoClick(orderId);
                  }}
                  loading={loadingOrders.includes(orderId)}
                >
                  Undo
                </Button>

                <Button
                  plain
                  onClick={(e) => {
                    e.stopPropagation();
                    window.open(
                      `https://admin.shopify.com/store/${storeName}/orders/${orderId}`
                    );
                  }}
                >
                  View Order
                </Button>
              </div>
            </IndexTable.Cell>
          </IndexTable.Row>
        );
      }
    );
  }

  return (
    <>
      <div
        style={{
          display: "flex",
          gap: "10px",
          marginTop: "20px",
          marginBottom: "20px",
          justifyContent: "flex-end",
        }}
      >
        <Button
          variant="secondary"
          onClick={() => {
            setRefreshButtonClicked(true);
          }}
          loading={refreshButtonClicked && loading}
        >
          Refresh List
        </Button>

        {/* <Button
          variant="secondary"
          onClick={handlePushAllClick}
          loading={isAllCreating}
        >
          Push All To ERP
        </Button> */}
        <Button
          onClick={handlePushSelectedClick}
          disabled={!selectedResources.length}
          loading={isCreating}
        >
          Push to ERP
        </Button>
      </div>

      <Modal
        open={showConfirmDialog}
        onClose={handleCancelPushAll}
        title="Confirm to Push the Selected Orders"
        primaryAction={{
          content: "Confirm",
          onAction: handleConfirmPushAll,
          destructive: true,
        }}
        secondaryActions={[
          {
            content: "Cancel",
            onAction: handleCancelPushAll,
          },
        ]}
      >
        <Modal.Section>
          <Text>
            Are you sure you want to push all orders to the ERP system?
          </Text>

          <div style={{ marginTop: "1rem" }}>
            <label
              htmlFor="erpInput"
              style={{ display: "block", marginBottom: "0.5rem" }}
            >
              Add Comments
            </label>
            <input
              id="erpInput"
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              style={{
                width: "100%",
                padding: "8px",
                fontSize: "14px",
                border: "1px solid #ccc",
                borderRadius: "4px",
              }}
            />
          </div>
        </Modal.Section>
      </Modal>

      <Modal
        open={showSelectedConfirmDialog}
        onClose={handleCancelPushSelected}
        title="Confirm Push Selected Orders"
        primaryAction={{
          content: "Confirm",
          onAction: handleConfirmPushSelected,
          destructive: true,
        }}
        secondaryActions={[
          {
            content: "Cancel",
            onAction: handleCancelPushSelected,
          },
        ]}
      >
        <Modal.Section>
          <Text>
            Are you sure you want to push all orders to ERP? This action cannot
            be undone.
          </Text>

          <div style={{ marginTop: "1rem" }}>
            <label
              htmlFor="erpInput"
              style={{ display: "block", marginBottom: "0.5rem" }}
            >
              Optional Note:
            </label>
            <input
              id="erpInput"
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              style={{
                width: "100%",
                padding: "8px",
                fontSize: "14px",
                border: "1px solid #ccc",
                borderRadius: "4px",
              }}
            />
          </div>
        </Modal.Section>
      </Modal>

      <Modal
        open={showUndoConfirmDialog}
        onClose={handleCancelUndo}
        title="Confirm Undo Order"
        primaryAction={{
          content: "Confirm",
          onAction: handleConfirmUndo,
          destructive: true,
        }}
        secondaryActions={[
          {
            content: "Cancel",
            onAction: handleCancelUndo,
          },
        ]}
      >
        <Modal.Section>
          <Text>
            Are you sure you want to undo this order? Before confirming, make
            sure all items in this order are deleted from Sage. Once confirmed,
            you can place the same order again in Sage.
          </Text>
        </Modal.Section>
      </Modal>

      <Card>
        <IndexFilters
          queryValue={queryValue}
          queryPlaceholder="Search orders"
          filters={filters}
          appliedFilters={appliedFilters}
          onQueryChange={handleFiltersQueryChange}
          onQueryClear={handleQueryValueRemove}
          onClearAll={handleClearFilter}
          mode={mode}
          setMode={setMode}
          tabs={tabs}
          cancelAction={{
            onAction: onHandleCancel,
            disabled: false,
            loading: false,
          }}
          selected={itemStrings.findIndex(
            (item) => item.toUpperCase() === selectedStatus
          )}
        />

        {isTableLoading ? (
          <div
            style={{
              display: "flex", // Use flexbox for centering
              justifyContent: "center", // Center horizontally
              alignItems: "center", // Center vertically
              minHeight: "300px", // Ensure height is enough to cover the table
              width: "100%", // Make the loader take the full width of the table
              margin: "0 auto", // Ensure loader is centered in its container
              textAlign: "center", // Center the text inside
              padding: "20px", // Padding for the loader area
            }}
          >
            <Spinner
              size="large"
              style={{ margin: "auto", textAlign: "center" }}
            />
            <p>Loading...</p>
          </div>
        ) : (
          <IndexTable
            condensed={useBreakpoints().smDown}
            resourceName={resourceName}
            itemCount={orders.length}
            selectedItemsCount={
              allResourcesSelected ? "All" : selectedResources.length
            }
            onSelectionChange={handleSelectionChange}
            headings={[
              { title: "Order Name", alignment: "center" },
              { title: "Date", alignment: "center" },
              { title: "Customer Name", alignment: "center" },
              { title: "Total Price", alignment: "center" },
              { title: "Payment Status", alignment: "center" },
              { title: "Total Item No.", alignment: "center" },
              { title: "Status", alignment: "center" },
              { title: "ECPAC Order No.", alignment: "center" },
              { title: "Actions", alignment: "center" },
            ]}
            pagination={
              totalPages > 1 && {
                hasNext: page < totalPages,
                onNext: () => {
                  setPage((prev) => prev + 1);
                },
                onPrevious: () => {
                  setPage((prev) => prev - 1);
                },
                hasPrevious: page > 1,
              }
            }
          >
            {rowMarkup}
          </IndexTable>
        )}
      </Card>
    </>
  );
};

function disambiguateLabel(key, value) {
  switch (key) {
    case "status":
      return `Status: ${value}`;
    default:
      return value;
  }
}
