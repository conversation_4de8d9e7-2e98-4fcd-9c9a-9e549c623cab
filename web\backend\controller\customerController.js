import { getATFromSQL } from "../utils/tokenHelper.js";
import axios from "axios";

export const updateCustomerDetails = async (req, res) => {
  try {
    const { customerId, firstName, lastName, phone, dateOfBirth } = req.body;

    if (!customerId || !firstName || !lastName) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: [
          {
            code: "MISSING_REQUIRED_FIELDS",
            message: "Customer ID, First Name, and Last Name are required",
          },
        ],
      });
    }

    let storeData = await getATFromSQL();
    let AT;
    storeData.map((x) => {
      if (x.shop === process.env.SHOP) {
        AT = x.accessToken;
      }
    });

    // Prepare input
    const input = {
      id: `gid://shopify/Customer/${customerId}`,
      firstName,
      lastName,
    };

    if (phone) {
      input.phone = phone;
    }

    if (dateOfBirth) {
      input.metafields = [
        {
          namespace: "custom",
          key: "date_of_birth",
          type: "date",
          value: dateOfBirth,
        },
      ];
    }

    const graphqlQuery = {
      query: `
            mutation customerUpdate($input: CustomerInput!) {
                customerUpdate(input: $input) {
                    customer {
                        id
                        firstName
                        lastName
                        phone
                        metafield(namespace: "custom", key: "date_of_birth") {
                            value
                        }
                    }
                    userErrors {
                        field
                        message
                    }
                }
            }
            `,
      variables: { input },
    };

    const response = await axios({
      method: "post",
      url: `https://${process.env.SHOP}/admin/api/${process.env.GRAPH_QL_API_VERSION}/graphql.json`,
      headers: {
        "Content-Type": "application/json",
        "X-Shopify-Access-Token": AT,
      },
      data: JSON.stringify(graphqlQuery),
    });

    if (response.data.data.customerUpdate.userErrors.length > 0) {
      return res.status(400).json({
        responseCode: 1,
        status: "error",
        errors: response.data.data.customerUpdate.userErrors.map((error) => ({
          code: "CUSTOMER_UPDATE_FAILED",
          message: error.message,
        })),
      });
    }

    res.status(200).json({
      responseCode: 0,
      status: "success",
      data: {
        message: "Customer updated successfully!",
        customer: response.data.data.customerUpdate.customer,
      },
    });
  } catch (error) {
    console.error("Error updating customer:", error);
    res.status(500).json({
      responseCode: 1,
      status: "error",
      errors: [
        {
          code: "INTERNAL_SERVER_ERROR",
          message: "Internal server error",
        },
      ],
    });
  }
};
