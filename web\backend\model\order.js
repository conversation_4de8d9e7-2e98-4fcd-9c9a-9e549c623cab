import mongoose from "mongoose";

const orderSchema = new mongoose.Schema(
  {
    fulfillment_id: { type: String, required: true },
    orderId: { type: String, required: true },
    orderName: { type: String },
    customerName: { type: String, default: "N/A" },
    status: { type: String, default: "PENDING" },
    totalPrice: { type: String },
    paymentStatus: { type: String },
    shopify_updated_At: { type: Date },
    shopify_created_at: { type: Date },
    erpIdentifier: { type: Number },
    orderNumber: { type: String },
    comment: String,
    lineItems: [
      {
        id: String,
        quantity: Number,
        sku: String,
        title: String,
        price: String,
        variantId: String,
        refunded: { type: Boolean, default: false },
        remainingQuantity: Number,
        productType: String,
        stringPrice: String,
        discount: String,
        orderName: String,
        refundDetails: [],
      },
    ],
    refunds: [
      {
        id: { type: Number, required: true },
        line_item_id: { type: Number, required: true },
        quantity: { type: Number, required: true },
      },
      { _id: false },
    ],
  },
  { timestamps: true }
);

const Order = mongoose.model("Order", orderSchema);
export default Order;
