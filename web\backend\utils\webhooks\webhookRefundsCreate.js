import getATFromSQL from "../getATFromSQL.js";
import fetchGraphqlDataShopify from "../fetchGraphqlDataShopify.js";
import { REFUNDS_CREATE_WEBHOOK } from "../../queries/internalQueries.js";
import * as dotenv from "dotenv";
dotenv.config();

export default async function subscribeRefundsCreateWebhook() {
  let storeData = await getATFromSQL();
  let storeAT;
  storeData.map((x) => {
    if (x.shop === process.env.SHOP) {
      storeAT = x.accessToken;
    }
  });

  const variables = {
    callbackUrl: `${process.env.HOST}/api/webhooks/refundscreate`,
  };

  const response = await fetchGraphqlDataShopify(
    REFUNDS_CREATE_WEBHOOK,
    variables,
    process.env.GRAPH_QL_API_VERSION
  );

  if (response.error) {
    console.log("Error creating webhook:", response.error);
    return;
  }

  console.log(JSON.stringify(response.data, undefined, 2));
}
