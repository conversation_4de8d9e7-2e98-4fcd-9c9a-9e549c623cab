import { join } from "path";
import { readFileSync } from "fs";
import express from "express";
import serveStatic from "serve-static";
import shopify from "./shopify.js";
import PrivacyWebhookHandlers from "./privacy.js";
import cron from "node-cron";
import { productScheduledService } from "./backend/service/cron/productScheduledService.js";
import router from "./backend/router/orderRouter.js";
import dotenv from "dotenv";
import connectDB from "./backend/DataBase/connectDataBase.js";
import { globalErrorHandler } from "./backend/utils/errorHandlers.js";
import webhookRouter from "./backend/router/webhooksRouter.js";
import subscribeRefundsCreateWebhook from "./backend/utils/webhooks/webhookRefundsCreate.js";
import subscribeOrdersFulfilledWebhook from "./backend/utils/webhooks/webhookOrderFulfill.js";
import { sendSuccess } from "./backend/utils/successHandler.js";
import { syncERPProducts } from "./backend/service/cron/productSyncFlow.js";
import { updateInventoryLevels } from "./backend/service/cron/InventorySyncFlow.js";
import { runCronJob } from "./backend/controller/cron.controller.js";
import cronRouter from "./backend/router/cron.router.js";
import CustomerRouter from "./backend/router/customerRouter.js";

dotenv.config();

const PORT = parseInt(
  process.env.BACKEND_PORT || process.env.PORT || "3000",
  10
);

const STATIC_PATH =
  process.env.NODE_ENV === "production"
    ? `${process.cwd()}/frontend/dist`
    : `${process.cwd()}/frontend/`;

connectDB();
const app = express();

// Set up Shopify authentication and webhook handling
app.get(shopify.config.auth.path, shopify.auth.begin());
app.get(
  shopify.config.auth.callbackPath,
  shopify.auth.callback(),
  shopify.redirectToShopifyOrAppRoot()
);
app.post(
  shopify.config.webhooks.path,
  shopify.processWebhooks({ webhookHandlers: PrivacyWebhookHandlers })
);

// If you are adding routes outside of the /api path, remember to
// also add a proxy rule for them in web/frontend/vite.config.js

subscribeRefundsCreateWebhook();
subscribeOrdersFulfilledWebhook();

app.use("/api/webhooks", webhookRouter);

app.use(express.json());
app.get("/healthcheck", (req, res) => {
  sendSuccess(res, { success: "Healthcheck working!!" });
});

app.use("/api/order", router);
app.use("/api/customer", CustomerRouter);

app.use("/api/cron", cronRouter);

cron.schedule("0 1 * * *", async () => {
  updateInventoryLevels();
});

// syncERPProducts()

// cron.schedule("0 1 * * *", async () => {
//   syncERPProducts();
// });

// cron.schedule("40 18 * * *", async () => {
//   productScheduledService(false, false);
// });

// Product sync cron (2:00 AM Singapore/Malaysia time)
// cron.schedule("0 18 * * *", async () => {
// runCronJob();
// });

app.use("/api/*", shopify.validateAuthenticatedSession());

app.use(globalErrorHandler);

app.use(shopify.cspHeaders());
app.use(serveStatic(STATIC_PATH, { index: false }));

app.use("/*", shopify.ensureInstalledOnShop(), async (_req, res, _next) => {
  return res
    .status(200)
    .set("Content-Type", "text/html")
    .send(
      readFileSync(join(STATIC_PATH, "index.html"))
        .toString()
        .replace("%VITE_SHOPIFY_API_KEY%", process.env.SHOPIFY_API_KEY || "")
    );
});

app.listen(PORT);
