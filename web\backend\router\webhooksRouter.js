import express from "express";
import verifyWebhook from "../middlewares/verifyWebhook.js";
import jsonParser from "../middlewares/jsonParser.js";
import {
  handleFulfilmentCreate,
  handleRefundCreate,
} from "../controller/webhooksController.js";

const webhookRouter = express.Router();

webhookRouter.post(
  "/orderfulfilled",
  express.raw({ type: "application/json" }),
  // verifyWebhook,  need to debug by pranav
  jsonParser,
  handleFulfilmentCreate
);

webhookRouter.post(
  "/refundscreate",
  express.raw({ type: "application/json" }),
  // verifyWebhook,  need to debug by pranav
  jsonParser,
  handleRefundCreate
);

export default webhookRouter;
