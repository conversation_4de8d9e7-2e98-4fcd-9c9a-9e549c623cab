import { useEffect, useState } from "react";
import { CREATE_ALL_ORDER_ON_ERP } from "../../Constant/orders";
import { useAppBridge } from "@shopify/app-bridge-react";

export const useCreateAllOrdersOnERP = (
  createAllErpButtonClicked,
  setCreateAllErpButtonClicked,
  comment,
  setInputValue
) => {
  const shopify = useAppBridge();
  const [isCreating, setIsCreating] = useState(false);
  const [isCreated, setIsCreated] = useState(false);
  const [errorInCreating, setErrorInCreating] = useState(false);

  useEffect(() => {
    const createOrder = async () => {
      try {
        setIsCreated(false);
        setIsCreating(true);
        setErrorInCreating(false);
        const response = await fetch(CREATE_ALL_ORDER_ON_ERP, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            comment,
          }),
        });

        setInputValue('')

        if (!response.ok) {
          shopify.toast.show(
            "Error in creating the orders on the ERP. Please try again later.",
            {
              isError: true,
              duration: 60000,
            }
          );
        }

        const data = await response.json();
        
        if (data?.data?.data?.failedOrders.length > 0) {
          shopify.toast.show(
            `Error occured while pushing to ERP. ${
              data?.data?.data?.failedOrders?.length
            } ${
              data?.data?.data?.failedOrders?.length > 1 ? "Orders are" : "Order is"
            } failed.`,
            {
              isError: true,
              duration: 60000,
            }
          );

          setErrorInCreating(true);
        } else {
          shopify.toast.show("Successfully pushed to the ERP.", {
            duration: 2000,
          });
          setIsCreated(true);
        }
      } catch (err) {
        setErrorInCreating(true);

        shopify.toast.show(
          "Error occured while pushing to ERP. Please try again later.",
          {
            duration: 60000,
            isError: true,
          }
        );
      } finally {
        setCreateAllErpButtonClicked(false);
        setIsCreating(false);
      }
    };

    if (createAllErpButtonClicked) {
      createOrder();
    }
  }, [createAllErpButtonClicked]);

  return { isCreated, isCreating, errorInCreating };
};
