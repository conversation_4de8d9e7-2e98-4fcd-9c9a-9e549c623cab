import axios from "axios";
import Order from "../../model/order.js";
import NewAppError from "../../utils/errorHandlers.js";

import FailedOrder from "../../model/erp.failed.order.model.js";

import {
  CUSTOMER_NUMBER,
  LOCATION,
  SERVICE_CHARGE_SKU,
  ORDER_REFERENCE,
  ORDER_TYPE,
  TAXAUTHORITY1,
} from "../../Constants/erpConstants.js";

const formatOrder = async (orderData) => {
  try {
    const { lineItems, _id } = orderData;
    const id = _id.toString();

    const lineItemsArr = lineItems.map((item) => {
      return {
        QuantityOrdered: Number(item.quantity),
        Item: item.sku,
        OrderDiscountAmount: Number(item.discount),
        PriceOverride: true,
        Location: LOCATION,
        ShipmentTrackingNumber: item?.orderName,
        ...(item.sku === SERVICE_CHARGE_SKU //anchal will tell the sku chnage in the future
          ? {
              LineType: "Miscellaneous",
              MiscellaneousChargesCode: "SHIP",
              ExtendedAmount: Number(item.price),
            }
          : { PricingUnitPrice: Number(item.price) }),
      };
    });
    return {
      id,
      lineItemsArr,
    };
  } catch (error) {
    throw new Error(
      `Formatting failed for orderID: ${orderData._id.toString()}.`
    );
  }
};
const createErpOrderService = async (orderData) => {
  const { lineItemsArr, id } = orderData;
  try {
    let data = JSON.stringify({
      ShipToLocationCode: "",
      CustomerNumber: CUSTOMER_NUMBER,
      OrderType: ORDER_TYPE,
      OrderDescription: orderData?.Description || "This is a new order.",
      OrderReference: ORDER_REFERENCE,
      OrderDiscountPercentage: orderData?.discount || 0,
      Salesperson1: "",
      TaxAuthority1: TAXAUTHORITY1,
      TaxClass1: 7,
      OrderComment: orderData?.comment || "",
      OrderDetails: lineItemsArr,
    });
    let config = {
      method: "post",
      maxBodyLength: Infinity,
      url: `${process.env.SAGE_API_ENDPOINT}/OE/OEOrders`,
      headers: {
        "Content-Type": `${process.env.API_CONTENT_TYPE}`,
        Accept: `${process.env.API_CONTENT_TYPE}`,
        Authorization: `${process.env.SAGE_BEARER_TOKEN}`,
      },
      data: data,
    };
    const response = await axios.request(config);
    const {
      OrderUniquifier,
      OrderNumber,
      CustomerNumber,
      OrderCompleted,
      SalespersonName1,
    } = response.data;
    const responseData = {
      data: {
        OrderUniquifier,
        OrderNumber,
        CustomerNumber,
        OrderCompleted,
        SalespersonName1,
        id,
      },
    };
    return responseData;
  } catch (error) {
    throw Error(
      `Error when creating order on ERP.Message:${
        error?.response?.data?.error?.message?.value || error.message
      }`
    );
  }
};

const getOrdersDataHandler = async (orderIds) => {
  try {
    // Query orders based on orderIds
    const orders = await Order.find({ _id: { $in: orderIds } });
    if (!orders) {
      return new NewAppError(
        "NOT_FOUND",
        "No orders found with the provided orderIds.",
        404
      );
    }
    // Return the found orders
    return {
      data: orders,
    };
  } catch (error) {
    console.error("Error while fetching orders.");
    return new NewAppError(
      "SERVER_ERROR",
      "Error while fetching saved orders data.",
      500
    );
  }
};

const updateOrderInDB = async (
  orderId,
  orderNumber,
  orderUniquifier,
  comment
) => {
  try {
    await Order.findByIdAndUpdate(orderId, {
      orderNumber,
      comment,
      status: "PUSHED",
      erpIdentifier: orderUniquifier,
    });
  } catch (error) {
    console.error(`Failed to update order ${orderId}:`, error.message);
  }
};
const getPendingOrdersHandler = async () => {
  try {
    // Fetch all orders where isErpUploaded is false
    const orders = await Order.find({ status: "PENDING" });
    if (!orders || orders.length === 0) {
      return new NewAppError("NOT_FOUND", "No pending orders found.", 404);
    }
    return { data: orders };
  } catch (error) {
    console.error("Error while fetching pending orders.");
    return new NewAppError(
      "SERVER_ERROR",
      "Error while fetching pending orders data.",
      500
    );
  }
};
const saveFailedOrders = async (failedOrders) => {
  if (failedOrders.length === 0) return;
  try {
    await FailedOrder.create({ failedOrders });
  } catch (error) {
    console.error("Error saving failed orders:", error.message);
  }
};
const createConsolidatedOrder = async (ordersData, comment) => {
  try {
    // Collect all non-refunded line items from all orders
    const consolidatedLineItems = [];
    for (const order of ordersData.data) {
      const nonRefundedItems = order.lineItems
        .filter((item) => item.refunded === false)
        .map((item) => ({
          ...item,
          orderName: order.orderName, // add orderName to each item
        }));
      consolidatedLineItems.push(...nonRefundedItems);
    }

    // Create a new order document
    const newOrder = new Order({
      orderName: `Consolidated Order ${new Date().toISOString()}`,
      customerName: "Consolidated Order",
      totalPrice: consolidatedLineItems.reduce(
        (sum, item) => sum + item.price * item.quantity,
        0
      ),
      paymentStatus: "paid",
      lineItems: consolidatedLineItems,
      shopify_updated_At: new Date(),
      shopify_created_At: new Date(),
      status: "PENDING",
      orderId: "6546623103271",
      fulfillment_id: "6546623103271",
      comment,
    });

    // Save the new order
    await newOrder.save();
    return newOrder;
  } catch (error) {
    throw new Error(`Error creating consolidated order: ${error.message}`);
  }
};
const deleteConsolidatedOrder = async (orderId) => {
  try {
    await Order.findByIdAndDelete(orderId);
  } catch (error) {
    throw new Error(`Error deleting consolidated order: ${error.message}`);
  }
};

const creditDebitNoteUpdate = async ({ refund_line_items = [], order_id }) => {
  if (!refund_line_items.length) {
    console.warn("No refund line items found.");
    return;
  }

  try {
    const url = `${process.env.SAGE_API_ENDPOINT}/OE/OECreditDebitNotes`;

    const creditDebitDetails = refund_line_items.map(({ line_item }) => ({
      LineType: "Item",
      Item: line_item.sku,
      Location: LOCATION,
      QuantityReturned: line_item.quantity,
      ReturnType: "ItemsReturnedToInventory",
      PriceOverride: true,
      PricingUnitPrice: Number(line_item.price),
    }));

    const payload = {
      CustomerNumber: CUSTOMER_NUMBER,
      DefaultLocationCode: LOCATION,
      Description: "Returned GOODS",
      CreditDebitNoteType: "CreditNote",
      CreditDebitDetails: creditDebitDetails,
    };

    const { data } = await axios.post(url, payload, {
      headers: {
        "Content-Type": `${process.env.API_CONTENT_TYPE}`,
        Accept: `${process.env.API_CONTENT_TYPE}`,
        Authorization: `${process.env.SAGE_BEARER_TOKEN}`,
      },
    });

    const returnedDetails = data.CreditDebitDetails || [];

    // Loop through and update each line item
    for (const item of refund_line_items) {
      const sku = item.line_item.sku;
      // Normalize SKUs by removing hyphens for comparison
      const normalizedSku = sku.replace(/-/g, "");
      const match = returnedDetails.find(
        (detail) => detail.Item.replace(/-/g, "") === normalizedSku
      );

      if (match) {
        const refundDetail = {
          cnUniquifier: match.CNUniquifier,
          // quantity: match.QuantityReturned,
        };

        try {
          const updateResult = await Order.updateOne(
            { orderId: order_id.toString(), "lineItems.sku": sku },
            {
              $push: {
                "lineItems.$.refundDetails": refundDetail,
              },
            }
          );
        } catch (updateError) {
          console.error(
            `Failed to update order ${order_id} for SKU: ${sku}:`,
            updateError
          );
        }
      }
    }

    console.log("Credit/Debit Note created and lineItems updated");
  } catch (error) {
    console.error(
      "Failed to update credit/debit note",
      error?.response?.data || error.message
    );
    throw error;
  }
};

export {
  createErpOrderService,
  formatOrder,
  getOrdersDataHandler,
  updateOrderInDB,
  saveFailedOrders,
  getPendingOrdersHandler,
  createConsolidatedOrder,
  deleteConsolidatedOrder,
  creditDebitNoteUpdate,
};
