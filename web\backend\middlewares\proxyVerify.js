import crypto from "crypto";
export const verifyProxy = async function (req, res, next) {
  if (req.query.shop) {
    if (!req.query.signature) {
      return res.sendStatus(403);
    }
    const signature = req.query.signature;
    const sharedSecret = process.env.SHOPIFY_API_SECRET;
    const def = req.query;
    delete def.signature;
    const sortedQuery = Object.keys(def)
      .map((key) => `${key}=${Array(def[key]).join(",")}`)
      .sort()
      .join("");
    const calculatedSignature = crypto
      .createHmac("sha256", sharedSecret)
      .update(sortedQuery)
      .digest("hex");
    if (calculatedSignature === signature) {
      console.log("validated");
      return next();
    }
    console.log("UnAuthorised Request");
    return res.sendStatus(403);
  } else {
    console.log("No Proxy Query Was Received");
    return res.sendStatus(403);
  }
};
