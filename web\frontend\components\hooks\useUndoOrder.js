import { UNDO_ORDER_ON_ERP } from "../../Constant/orders";

export const useUndoOrder = async (orderId, setLoadingOrders, setRefreshButtonClicked) => {
  try {

    const originalOrderName = orderId; // This keeps the "#1234" format

    setLoadingOrders((prev) => [...prev, originalOrderName]);

    const response = await fetch(`${UNDO_ORDER_ON_ERP}?orderId=${orderId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      shopify.toast.show(
        "Error in creating the orders on the ERP. Please try again later.",
        {
          isError: true,
          duration: 60000,
        }
      );
    } else {
      shopify.toast.show("Successfully undone the order.", {
        duration: 2000,
      });
      setRefreshButtonClicked(true)
    }
  } catch (error) {
    console.error("Failed to undo order:", error);
  } finally {
    setLoadingOrders((prev) => prev.filter((name) => name !== orderId));
  }
};
