import { Card, Page, Layout } from "@shopify/polaris";
import { TitleBar } from "@shopify/app-bridge-react";
import { useTranslation, Trans } from "react-i18next";

import { trophyImage } from "../assets";

import { ProductsCard } from "../components";
import ProductSyncWrapper from "./productsync";

export default function HomePage() {
  const { t } = useTranslation();
  return (
    <Page narrowWidth>
      <TitleBar title="Sun Rise" />
      <Layout>
        <Layout.Section>
          <Card sectioned>
            <text>Sun Rise App </text>
          </Card>
          <ProductSyncWrapper/>
        </Layout.Section>
      </Layout>
    </Page>
  );
}
