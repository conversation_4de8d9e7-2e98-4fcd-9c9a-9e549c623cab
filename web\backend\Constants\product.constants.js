export const PRODUCTS = [
  {
    UnformattedItemNumber: "BAG22926TFEZSAFRONZZZZ",
    ItemNumber: "BAG22926TFEZ-SAFRON-ZZZZ",
    Description: "YONEX BAG BT6 FLAT EMBROIDERY # PC2-Q014-22926TF-SR",
    Model: "YONEX BAG BT6 FLAT EMBROIDERY # PC2-Q014-22926TF-SR",
    Color: "SAFFRON",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 10.0,
    SCK_QuantityAvailable: 10.0,
    BasePrice: null,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22926TFEZSPHNVZZZZZ",
    ItemNumber: "BAG22926TFEZ-SPHNVZ-ZZZZ",
    Description: "YONEX BAG BT6 FLAT EMBROIDERY # PC2-Q014-22926TF-SR",
    Model: "YONEX BAG BT6 FLAT EMBROIDERY # PC2-Q014-22926TF-SR",
    Color: "SAPPHIRE NAVY",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 10.0,
    SCK_QuantityAvailable: 10.0,
    BasePrice: null,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22926TFEZTANGRDZZZZ",
    ItemNumber: "BAG22926TFEZ-TANGRD-ZZZZ",
    Description: "YONEX BAG BT6 FLAT EMBROIDERY # PC2-Q014-22926TF-SR",
    Model: "YONEX BAG BT6 FLAT EMBROIDERY # PC2-Q014-22926TF-SR",
    Color: "TANGO RED",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 10.0,
    SCK_QuantityAvailable: 10.0,
    BasePrice: null,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22926TZC3BKPRRDZZZZ",
    ItemNumber: "BAG22926TZC3-BKPRRD-ZZZZ",
    Description: "YONEX BAG # PC3-Q014-22926T-SR",
    Model: "YONEX BAG # PC3-Q014-22926T-SR",
    Color: "BLACK / PERSIAN RED",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 9.0,
    SCK_QuantityAvailable: 9.0,
    BasePrice: 64.27,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22926TZC3BLZZZZZZZZ",
    ItemNumber: "BAG22926TZC3-BLZZZZ-ZZZZ",
    Description: "YONEX BAG # PC3-Q014-22926T-SR",
    Model: "YONEX BAG # PC3-Q014-22926T-SR",
    Color: "BLUE",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 9.0,
    SCK_QuantityAvailable: 9.0,
    BasePrice: 64.27,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22926TZC3DFLGYLZZZZ",
    ItemNumber: "BAG22926TZC3-DFLGYL-ZZZZ",
    Description: "YONEX BAG # PC3-Q014-22926T-SR",
    Model: "YONEX BAG # PC3-Q014-22926T-SR",
    Color: "DARK FLOWER GREY / YELLOW",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 10.0,
    SCK_QuantityAvailable: 10.0,
    BasePrice: 64.27,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22926TZZZBKZZZZZZZZ",
    ItemNumber: "BAG22926TZZZ-BKZZZZ-ZZZZ",
    Description: "YONEX BAG BT6 # PC2-3D-Q014-22926T-SR",
    Model: "YONEX BAG BT6 # PC2-3D-Q014-22926T-SR",
    Color: "BLACK",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 0.0,
    SCK_QuantityAvailable: 0.0,
    BasePrice: 64.86,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22926TZZZCDRSZZZZZZ",
    ItemNumber: "BAG22926TZZZ-CDRSZZ-ZZZZ",
    Description: "YONEX BAG BT6 # PC2-3D-Q014-22926T-SR",
    Model: "YONEX BAG BT6 # PC2-3D-Q014-22926T-SR",
    Color: "CREDDISH ROSE",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 10.0,
    SCK_QuantityAvailable: 10.0,
    BasePrice: 64.86,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22926TZZZMSPUZZZZZZ",
    ItemNumber: "BAG22926TZZZ-MSPUZZ-ZZZZ",
    Description: "YONEX BAG BT6 # PC2-3D-Q014-22926T-SR",
    Model: "YONEX BAG BT6 # PC2-3D-Q014-22926T-SR",
    Color: "MIST PURPLE",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 10.0,
    SCK_QuantityAvailable: 10.0,
    BasePrice: 64.86,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22926TZZZNBZZZZZZZZ",
    ItemNumber: "BAG22926TZZZ-NBZZZZ-ZZZZ",
    Description: "YONEX BAG BT6 # PC2-3D-Q014-22926T-SR",
    Model: "YONEX BAG BT6 # PC2-3D-Q014-22926T-SR",
    Color: "FINE BLUE",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 7.0,
    SCK_QuantityAvailable: 7.0,
    BasePrice: 64.86,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22926TZZZNVSAXEZZZZ",
    ItemNumber: "BAG22926TZZZ-NVSAXE-ZZZZ",
    Description: "YONEX BAG BT6 # PC2-3D-Q014-22926T-SR",
    Model: "YONEX BAG BT6 # PC2-3D-Q014-22926T-SR",
    Color: "NAVY/SAXE",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 10.0,
    SCK_QuantityAvailable: 10.0,
    BasePrice: 64.86,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22926TZZZSAFRONZZZZ",
    ItemNumber: "BAG22926TZZZ-SAFRON-ZZZZ",
    Description: "YONEX BAG BT6 # PC2-3D-Q014-22926T-SR",
    Model: "YONEX BAG BT6 # PC2-3D-Q014-22926T-SR",
    Color: "SAFFRON",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 10.0,
    SCK_QuantityAvailable: 10.0,
    BasePrice: 64.86,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22926TZZZSPHNVZZZZZ",
    ItemNumber: "BAG22926TZZZ-SPHNVZ-ZZZZ",
    Description: "YONEX BAG BT6 # PC2-3D-Q014-22926T-SR",
    Model: "YONEX BAG BT6 # PC2-3D-Q014-22926T-SR",
    Color: "SAPPHIRE NAVY",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 10.0,
    SCK_QuantityAvailable: 10.0,
    BasePrice: 64.86,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22926TZZZTANGRDZZZZ",
    ItemNumber: "BAG22926TZZZ-TANGRD-ZZZZ",
    Description: "YONEX BAG BT6 # PC2-3D-Q014-22926T-SR",
    Model: "YONEX BAG BT6 # PC2-3D-Q014-22926T-SR",
    Color: "TANGO RED",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 10.0,
    SCK_QuantityAvailable: 10.0,
    BasePrice: 64.86,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22929TZC3BKPRRDZZZZ",
    ItemNumber: "BAG22929TZC3-BKPRRD-ZZZZ",
    Description: "YONEX BAG # PC3-Q014-22929T-SR",
    Model: "YONEX BAG # PC3-Q014-22929T-SR",
    Color: "BLACK / PERSIAN RED",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 5.0,
    SCK_QuantityAvailable: 5.0,
    BasePrice: 72.11,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22929TZC3BLZZZZZZZZ",
    ItemNumber: "BAG22929TZC3-BLZZZZ-ZZZZ",
    Description: "YONEX BAG # PC3-Q014-22929T-SR",
    Model: "YONEX BAG # PC3-Q014-22929T-SR",
    Color: "BLUE",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 3.0,
    SCK_QuantityAvailable: 3.0,
    BasePrice: 72.11,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22929TZC3DFLGYLZZZZ",
    ItemNumber: "BAG22929TZC3-DFLGYL-ZZZZ",
    Description: "YONEX BAG # PC3-Q014-22929T-SR",
    Model: "YONEX BAG # PC3-Q014-22929T-SR",
    Color: "DARK FLOWER GREY / YELLOW",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 4.0,
    SCK_QuantityAvailable: 4.0,
    BasePrice: 72.11,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22929TZZZBKZZZZZZZZ",
    ItemNumber: "BAG22929TZZZ-BKZZZZ-ZZZZ",
    Description: "YONEX BAG BT9 # PC2-3D-Q014-22929T-SR",
    Model: "YONEX BAG BT9 # PC2-3D-Q014-22929T-SR",
    Color: "BLACK",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 10.0,
    SCK_QuantityAvailable: 10.0,
    BasePrice: 72.78,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22929TZZZCDRSZZZZZZ",
    ItemNumber: "BAG22929TZZZ-CDRSZZ-ZZZZ",
    Description: "YONEX BAG BT9 # PC2-3D-Q014-22929T-SR",
    Model: "YONEX BAG BT9 # PC2-3D-Q014-22929T-SR",
    Color: "CREDDISH ROSE",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 5.0,
    SCK_QuantityAvailable: 5.0,
    BasePrice: 72.78,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22929TZZZMSPUZZZZZZ",
    ItemNumber: "BAG22929TZZZ-MSPUZZ-ZZZZ",
    Description: "YONEX BAG BT9 # PC2-3D-Q014-22929T-SR",
    Model: "YONEX BAG BT9 # PC2-3D-Q014-22929T-SR",
    Color: "MIST PURPLE",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 10.0,
    SCK_QuantityAvailable: 10.0,
    BasePrice: 72.78,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22929TZZZNBZZZZZZZZ",
    ItemNumber: "BAG22929TZZZ-NBZZZZ-ZZZZ",
    Description: "YONEX BAG BT9 # PC1-3D-Q014-22929T-SR",
    Model: "YONEX BAG BT9 # PC2-3D-Q014-22929T-SR",
    Color: "FINE BLUE",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 10.0,
    SCK_QuantityAvailable: 10.0,
    BasePrice: 72.78,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22929TZZZNVSAXEZZZZ",
    ItemNumber: "BAG22929TZZZ-NVSAXE-ZZZZ",
    Description: "YONEX BAG BT9 # PC1-3D-Q014-22929T-SR",
    Model: "YONEX BAG BT9 # PC2-3D-Q014-22929T-SR",
    Color: "NAVY/SAXE",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 10.0,
    SCK_QuantityAvailable: 10.0,
    BasePrice: 72.78,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22929TZZZSAFRONZZZZ",
    ItemNumber: "BAG22929TZZZ-SAFRON-ZZZZ",
    Description: "YONEX BAG BT9 # PC2-3D-Q014-22929T-SR",
    Model: "YONEX BAG BT9 # PC2-3D-Q014-22929T-SR",
    Color: "SAFFRON",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 2.0,
    SCK_QuantityAvailable: 2.0,
    BasePrice: 72.78,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22929TZZZSPHNVZZZZZ",
    ItemNumber: "BAG22929TZZZ-SPHNVZ-ZZZZ",
    Description: "YONEX BAG BT9 # PC2-3D-Q014-22929T-SR",
    Model: "YONEX BAG BT9 # PC2-3D-Q014-22929T-SR",
    Color: "SAPPHIRE NAVY",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 5.0,
    SCK_QuantityAvailable: 5.0,
    BasePrice: 72.78,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22929TZZZTANGRDZZZZ",
    ItemNumber: "BAG22929TZZZ-TANGRD-ZZZZ",
    Description: "YONEX BAG BT9 # PC1-3D-Q014-22929T-SR",
    Model: "YONEX BAG BT9 # PC2-3D-Q014-22929T-SR",
    Color: "TANGO RED",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 1.0,
    SCK_QuantityAvailable: 1.0,
    BasePrice: 72.78,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22931WTC3BKPRRDZZZZ",
    ItemNumber: "BAG22931WTC3-BKPRRD-ZZZZ",
    Description: "YONEX TOURNAMENT BAG # PC3-Q014-22931WT-SR",
    Model: "YONEX TOURNAMENT BAG # PC3-Q014-22931WT-SR",
    Color: "BLACK / PERSIAN RED",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 10.0,
    SCK_QuantityAvailable: 10.0,
    BasePrice: 47.01,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22931WTC3BLZZZZZZZZ",
    ItemNumber: "BAG22931WTC3-BLZZZZ-ZZZZ",
    Description: "YONEX TOURNAMENT BAG # PC3-Q014-22931WT-SR",
    Model: "YONEX TOURNAMENT BAG # PC3-Q014-22931WT-SR",
    Color: "BLUE",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 1.0,
    SCK_QuantityAvailable: 1.0,
    BasePrice: 47.01,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22931WTC3DFLGYLZZZZ",
    ItemNumber: "BAG22931WTC3-DFLGYL-ZZZZ",
    Description: "YONEX TOURNAMENT BAG # PC3-Q014-22931WT-SR",
    Model: "YONEX TOURNAMENT BAG # PC3-Q014-22931WT-SR",
    Color: "DARK FLOWER GREY / YELLOW",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 9.0,
    SCK_QuantityAvailable: 9.0,
    BasePrice: 47.01,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22931WTFECDRSZZZZZZ",
    ItemNumber: "BAG22931WTFE-CDRSZZ-ZZZZ",
    Description: "YONEX TOURNAMENT BAG FLAT EMBROIDERY # PC2-Q014-22931WTF-SR",
    Model: "YONEX TOURNAMENT BAG FLAT EMBROIDERY # PC2-Q014-22931WTF-SR",
    Color: "CREDDISH ROSE",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 10.0,
    SCK_QuantityAvailable: 10.0,
    BasePrice: null,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22931WTFEMSPUZZZZZZ",
    ItemNumber: "BAG22931WTFE-MSPUZZ-ZZZZ",
    Description: "YONEX TOURNAMENT BAG FLAT EMBROIDERY # PC2-Q014-22931WTF-SR",
    Model: "YONEX TOURNAMENT BAG FLAT EMBROIDERY # PC2-Q014-22931WTF-SR",
    Color: "MIST PURPLE",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 10.0,
    SCK_QuantityAvailable: 10.0,
    BasePrice: null,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22931WTFESAFRONZZZZ",
    ItemNumber: "BAG22931WTFE-SAFRON-ZZZZ",
    Description: "YONEX TOURNAMENT BAG FLAT EMBROIDERY # PC2-Q014-22931WTF-SR",
    Model: "YONEX TOURNAMENT BAG FLAT EMBROIDERY # PC2-Q014-22931WTF-SR",
    Color: "SAFFRON",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 10.0,
    SCK_QuantityAvailable: 10.0,
    BasePrice: null,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22931WTFESPHNVZZZZZ",
    ItemNumber: "BAG22931WTFE-SPHNVZ-ZZZZ",
    Description: "YONEX TOURNAMENT BAG FLAT EMBROIDERY # PC2-Q014-22931WTF-SR",
    Model: "YONEX TOURNAMENT BAG FLAT EMBROIDERY # PC2-Q014-22931WTF-SR",
    Color: "SAPPHIRE NAVY",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 2.0,
    SCK_QuantityAvailable: 2.0,
    BasePrice: null,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22931WTZZBKZZZZZZZZ",
    ItemNumber: "BAG22931WTZZ-BKZZZZ-ZZZZ",
    Description: "YONEX TOURNAMENTBAG # PC1-3D-Q014-22931WT-SR",
    Model: "YONEX TOURNAMENT BAG # PC2-3D-Q014-22931WT-SR",
    Color: "BLACK",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 10.0,
    SCK_QuantityAvailable: 10.0,
    BasePrice: 47.45,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22931WTZZCDRSZZZZZZ",
    ItemNumber: "BAG22931WTZZ-CDRSZZ-ZZZZ",
    Description: "YONEX TOURNAMENT BAG # PC2-3D-Q014-22931WT-SR",
    Model: "YONEX TOURNAMENT BAG # PC2-3D-Q014-22931WT-SR",
    Color: "CREDDISH ROSE",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 10.0,
    SCK_QuantityAvailable: 10.0,
    BasePrice: 47.45,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22931WTZZMSPUZZZZZZ",
    ItemNumber: "BAG22931WTZZ-MSPUZZ-ZZZZ",
    Description: "YONEX TOURNAMENT BAG # PC2-3D-Q014-22931WT-SR",
    Model: "YONEX TOURNAMENT BAG # PC2-3D-Q014-22931WT-SR",
    Color: "MIST PURPLE",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 5.0,
    SCK_QuantityAvailable: 5.0,
    BasePrice: 47.45,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22931WTZZNBZZZZZZZZ",
    ItemNumber: "BAG22931WTZZ-NBZZZZ-ZZZZ",
    Description: "YONEX TOURNAMENTBAG # PC1-3D-Q014-22931WT-SR",
    Model: "YONEX TOURNAMENT BAG # PC2-3D-Q014-22931WT-SR",
    Color: "FINE BLUE",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 10.0,
    SCK_QuantityAvailable: 10.0,
    BasePrice: 47.45,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22931WTZZNVSAXEZZZZ",
    ItemNumber: "BAG22931WTZZ-NVSAXE-ZZZZ",
    Description: "YONEX TOURNAMENT BAG # PC1-3D-Q014-22931WT-SR",
    Model: "YONEX TOURNAMENT BAG # PC2-3D-Q014-22931WT-SR",
    Color: "NAVY/SAXE",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 0.0,
    SCK_QuantityAvailable: 0.0,
    BasePrice: 47.45,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22931WTZZSAFRONZZZZ",
    ItemNumber: "BAG22931WTZZ-SAFRON-ZZZZ",
    Description: "YONEX TOURNAMENT BAG # PC2-3D-Q014-22931WT-SR",
    Model: "YONEX TOURNAMENT BAG # PC2-3D-Q014-22931WT-SR",
    Color: "SAFFRON",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 0.0,
    SCK_QuantityAvailable: 0.0,
    BasePrice: 47.45,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22931WTZZSPHNVZZZZZ",
    ItemNumber: "BAG22931WTZZ-SPHNVZ-ZZZZ",
    Description: "YONEX TOURNAMENT BAG # PC2-3D-Q014-22931WT-SR",
    Model: "YONEX TOURNAMENT BAG # PC2-3D-Q014-22931WT-SR",
    Color: "SAPPHIRE NAVY",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 0.0,
    SCK_QuantityAvailable: 0.0,
    BasePrice: 47.45,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG22931WTZZTANGRDZZZZ",
    ItemNumber: "BAG22931WTZZ-TANGRD-ZZZZ",
    Description: "YONEX TOURNAMENT BAG # PC1-3D-Q014-22931WT-SR",
    Model: "YONEX TOURNAMENT BAG # PC2-3D-Q014-22931WT-SR",
    Color: "TANGO RED",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 0.0,
    SCK_QuantityAvailable: 0.0,
    BasePrice: 47.45,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG2312T01BPBKLTLMZZZZ",
    ItemNumber: "BAG2312T01BP-BKLTLM-ZZZZ",
    Description: "YONEX BACKPACK # ACE1-Q014-2312-T01-S",
    Model: "YONEX BACKPACK # ACE1-Q014-2312-T01-S",
    Color: "BLACK/LIGHT LIME",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 16.0,
    SCK_QuantityAvailable: 16.0,
    BasePrice: 13.02,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG2312T01BPDKCVPYZZZZ",
    ItemNumber: "BAG2312T01BP-DKCVPY-ZZZZ",
    Description: "YONEX BACKPACK # ACE1-Q014-2312-T01-S",
    Model: "YONEX BACKPACK # ACE1-Q014-2312-T01-S",
    Color: "DARK COBALT/VERI PERRY",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 15.0,
    SCK_QuantityAvailable: 15.0,
    BasePrice: 13.02,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG2312T01BPGYCHTMZZZZ",
    ItemNumber: "BAG2312T01BP-GYCHTM-ZZZZ",
    Description: "YONEX BACKPACK # ACE1-Q014-2312-T01-S",
    Model: "YONEX BACKPACK # ACE1-Q014-2312-T01-S",
    Color: "GRAY/CHERRY TOMATO",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 19.0,
    SCK_QuantityAvailable: 19.0,
    BasePrice: 13.02,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG2312T01BPINFHYRZZZZ",
    ItemNumber: "BAG2312T01BP-INFHYR-ZZZZ",
    Description: "YONEX BACKPACK # ACE1-Q014-2312-T01-S",
    Model: "YONEX BACKPACK # ACE1-Q014-2312-T01-S",
    Color: "INDIGO FOG/HYPER RED",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 18.0,
    SCK_QuantityAvailable: 18.0,
    BasePrice: 13.02,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG2312T02BPBKLTLMZZZZ",
    ItemNumber: "BAG2312T02BP-BKLTLM-ZZZZ",
    Description: "YONEX BACKPACK # ACE1-Q014-2312-T02-S",
    Model: "YONEX BACKPACK # ACE1-Q014-2312-T02-S",
    Color: "BLACK/LIGHT LIME",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 17.0,
    SCK_QuantityAvailable: 17.0,
    BasePrice: 13.02,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG2312T02BPNVGDKWZZZZ",
    ItemNumber: "BAG2312T02BP-NVGDKW-ZZZZ",
    Description: "YONEX BACKPACK # ACE1-Q014-2312-T02-S",
    Model: "YONEX BACKPACK # ACE1-Q014-2312-T02-S",
    Color: "NAVY / GOLDEN KIWI",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 23.0,
    SCK_QuantityAvailable: 23.0,
    BasePrice: 13.02,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG2312T02BPRCRDWHZZZZ",
    ItemNumber: "BAG2312T02BP-RCRDWH-ZZZZ",
    Description: "YONEX BACKPACK # ACE1-Q014-2312-T02-S",
    Model: "YONEX BACKPACK # ACE1-Q014-2312-T02-S",
    Color: "RACING RED / WHITE",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 21.0,
    SCK_QuantityAvailable: 21.0,
    BasePrice: 13.02,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG2312T02BPSEBLWHZZZZ",
    ItemNumber: "BAG2312T02BP-SEBLWH-ZZZZ",
    Description: "YONEX BACKPACK # ACE1-Q014-2312-T02-S",
    Model: "YONEX BACKPACK # ACE1-Q014-2312-T02-S",
    Color: "SEA BLUE / WHITE",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 21.0,
    SCK_QuantityAvailable: 21.0,
    BasePrice: 13.02,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG2312T03BPBKZZZZZZZZ",
    ItemNumber: "BAG2312T03BP-BKZZZZ-ZZZZ",
    Description: "YONEX BACKPACK # ACE1-Q014-2312-T03-S",
    Model: "YONEX BACKPACK # ACE1-Q014-2312-T03-S",
    Color: "BLACK",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 26.0,
    SCK_QuantityAvailable: 26.0,
    BasePrice: 13.02,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG2312T03BPDEEPCOZZZZ",
    ItemNumber: "BAG2312T03BP-DEEPCO-ZZZZ",
    Description: "YONEX BACKPACK # ACE1-Q014-2312-T03-S",
    Model: "YONEX BACKPACK # ACE1-Q014-2312-T03-S",
    Color: "DEEP COBALT",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 28.0,
    SCK_QuantityAvailable: 28.0,
    BasePrice: 13.02,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG2312T03BPDRKSEAZZZZ",
    ItemNumber: "BAG2312T03BP-DRKSEA-ZZZZ",
    Description: "YONEX BACKPACK # ACE1-Q014-2312-T03-S",
    Model: "YONEX BACKPACK # ACE1-Q014-2312-T03-S",
    Color: "DARK SEA",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 30.0,
    SCK_QuantityAvailable: 30.0,
    BasePrice: 13.02,
    S085_currency: "SGD",
  },
  {
    UnformattedItemNumber: "BAG2312T03BPSYLMZZZZZZ",
    ItemNumber: "BAG2312T03BP-SYLMZZ-ZZZZ",
    Description: "YONEX BACKPACK # ACE1-Q014-2312-T03-S",
    Model: "YONEX BACKPACK # ACE1-Q014-2312-T03-S",
    Color: "SUNNY LIME",
    Size: "-",
    Status: "Active",
    AccountSetCode: "YONEX",
    DefaultPriceListCode: "BRM01",
    StockingUnitOfMeasure: "PCS",
    CostingMethod: "MovingAverage",
    SCK_QuantityOnHand: 28.0,
    SCK_QuantityAvailable: 28.0,
    BasePrice: 13.02,
    S085_currency: "SGD",
  },
];
