import NewAppError from "../utils/errorHandlers.js";
import {
  createErpOrderService,
  formatOrder,
  getOrdersDataHandler,
  updateOrderInDB,
  saveFailedOrders,
  getPendingOrdersHandler,
  createConsolidatedOrder,
  deleteConsolidatedOrder,
} from "../service/sageOrders/sageOrderService.js";
import { sendSuccess } from "../utils/successHandler.js";

const createERPOrderController = async (req, res, next) => {
  try {
    const { orderIds, comment } = req.body;

    if (!Array.isArray(orderIds) || orderIds.length === 0) {
      return next(
        new NewAppError("INVALID_INPUT", "Invalid or empty orderIds.", 400)
      );
    }
    const ordersData = await getOrdersDataHandler(orderIds);
    if (ordersData instanceof NewAppError) {
      return next(ordersData);
    }
    const createdOrders = [];
    const failedOrders = [];
    try {
      const consolidatedOrder = await createConsolidatedOrder(
        ordersData,
        comment
      );
      // Process the consolidated order
      const formattedOrder = await formatOrder(consolidatedOrder);
      // Delete the consolidated order
      await deleteConsolidatedOrder(consolidatedOrder._id);
      const response = await createErpOrderService(formattedOrder);
      const { id, OrderNumber, OrderUniquifier } = response.data;

      // Update original orders that have non-refunded items
      for (const order of ordersData.data) {
        const hasNonRefundedItems = order.lineItems.some(
          (item) => item.refunded === false
        );
        if (hasNonRefundedItems) {
          await updateOrderInDB(
            order._id,
            OrderNumber,
            OrderUniquifier,
            comment
          );
        }
      }

      createdOrders.push({ id, message: "Created consolidated order on ERP." });
    } catch (error) {
      console.log(error.message, "message: Erp order creation failed");
      failedOrders.push({
        id: "unknown",
        error: error.message,
      });
    }

    return sendSuccess(res, {
      data: { createdOrders, failedOrders },
    });
  } catch (error) {
    console.log(error.message, "errorerror");
    return next(
      new NewAppError(
        "SERVER_ERROR",
        "Error while creating orders on ERP.",
        500
      )
    );
  }
};

const createAllPendingOrdersOnErpController = async (req, res, next) => {
  try {
    const ordersData = await getPendingOrdersHandler();
    const { comment } = req.body;

    if (ordersData instanceof NewAppError) {
      return next(ordersData);
    }

    const createdOrders = [];
    const failedOrders = [];

    try {
      // Create consolidated order using the service
      const consolidatedOrder = await createConsolidatedOrder(
        ordersData,
        comment
      );

      // Process the consolidated order
      const formattedOrder = await formatOrder(consolidatedOrder);

      // Delete the consolidated order
      await deleteConsolidatedOrder(consolidatedOrder._id);
      const response = await createErpOrderService(formattedOrder);
      const { id, OrderNumber, OrderUniquifier } = response.data;

      // Update original orders that have non-refunded items
      for (const order of ordersData.data) {
        const hasNonRefundedItems = order.lineItems.some(
          (item) => item.refunded === false
        );
        if (hasNonRefundedItems) {
          await updateOrderInDB(
            order._id,
            OrderNumber,
            OrderUniquifier,
            comment
          );
        }
      }

      createdOrders.push({ id, message: "Created consolidated order on ERP." });
    } catch (error) {
      console.log(error.message, "message: Erp order creation failed");
      failedOrders.push({
        id: "unknown",
        error: error.message,
      });
    }

    // Save failed orders if any
    if (failedOrders.length > 0) {
      await saveFailedOrders(failedOrders);
    }

    let responsePayload = {
      responseCode: 0,
      status: "success",
      data: { createdOrders, failedOrders },
    };
    sendSuccess(res, responsePayload);
  } catch (error) {
    console.log(error.message, "error");
    return next(
      new NewAppError(
        "SERVER_ERROR",
        "Error while creating orders on ERP.",
        500
      )
    );
  }
};

export { createERPOrderController, createAllPendingOrdersOnErpController };
