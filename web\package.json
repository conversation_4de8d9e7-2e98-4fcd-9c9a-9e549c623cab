{"name": "shopify-app-template-node", "version": "1.0.0", "private": true, "license": "UNLICENSED", "scripts": {"debug": "node --inspect-brk index.js", "dev": "cross-env NODE_ENV=development nodemon index.js --ignore ./frontend", "serve": "cross-env NODE_ENV=production node index.js"}, "type": "module", "engines": {"node": ">=16.13.0"}, "dependencies": {"@shopify/shopify-app-express": "^5.0.8", "@shopify/shopify-app-session-storage-sqlite": "^4.0.8", "axios": "^1.8.2", "compression": "^1.7.4", "cross-env": "^7.0.3", "dotenv": "^16.4.7", "mongoose": "^8.12.1", "node-cron": "^3.0.3", "serve-static": "^1.14.1"}, "devDependencies": {"nodemon": "^2.0.15", "prettier": "^2.6.2", "pretty-quick": "^3.1.3"}}