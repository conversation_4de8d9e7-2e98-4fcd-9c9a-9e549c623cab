name: Deploy Shopify App

on:
  push:
    branches: [main]  # or dev

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Deploy via SSH
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.EC2_DEV_HOST }}
          username: ${{ secrets.EC2_DEV_USERNAME }}
          key: ${{ secrets.EC2_DEV_SSH_KEY }}
          script: |
            set -e

            cd /home/<USER>/sunrise-b2c-app-dev

            # Pull latest code
            git pull origin main

            # Set root .env
            echo "${{ secrets.ROOT_ENV_FILE }}" > .env

            # Build frontend
            cd web/frontend
            npm install
            SHOPIFY_API_KEY=${{ secrets.SHOPIFY_API_KEY }} npm run build

            # Install backend
            cd ..
            echo "${{ secrets.WEB_ENV_FILE }}" > .env
            npm install

            # Restart app
            pm2 restart sunrise-b2c-dev || pm2 start index.js --name "sunrise-b2c-dev"
