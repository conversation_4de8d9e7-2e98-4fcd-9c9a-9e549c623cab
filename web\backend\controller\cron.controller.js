import CronLog from "../model/cronData.model.js";
import { updateInventoryLevels } from "../service/cron/InventorySyncFlow.js";
import { catchAsync } from "../utils/helpers.js";

export async function runCronJob(triggeredBy = "cron", res = null) {
  // Check if a run is already in progress

  const latestRun = await CronLog.findOne().sort({ createdAt: -1 });

  if (latestRun && !latestRun.endTime) {
    return { status: "skipped", message: "Skipped already running" };
  }

  // Insert a new cron log with status "running"
  const newRun = await CronLog.create({
    startTime: new Date(),
    triggeredBy,
    status: "running",
  });

  if (res) {
    res.status(200).json({
      responseCode: 0,
      status: "success",
      data: {
        message: "Cron job started",
      },
    });
  }

  try {
    // Use inventory sync logic and get stats
    const {
      totalSkus = null,
      updatedSkus = null,
      skippedSkus = null,
      batchCount = null,
    } = await updateInventoryLevels();
    const endTime = new Date();
    const duration = endTime - newRun.startTime;

    await CronLog.findByIdAndUpdate(newRun._id, {
      endTime,
      duration,
      status: "success",
      totalSkus,
      updatedSkus,
      skippedSkus,
      completedPages: batchCount,
    });

    const msg = `Success: completed in ${duration} ms`;
    return { status: "success", message: msg, duration };
  } catch (err) {
    const endTime = new Date();
    const duration = endTime - newRun.startTime;

    await CronLog.findByIdAndUpdate(newRun._id, {
      endTime,
      duration,
      status: "failed",
    });

    const msg = `Failed: ${err.message}`;
    return { status: "failed", message: msg, duration };
  }
}

export const runCronManually = catchAsync(async (req, res, next) => {
  const result = await runCronJob("manual", res);

  if (result.status === "skipped") {
    return res.status(207).json({
      responseCode: 0,
      status: "success",
      data: {
        message: "Cron job already running",
      },
    });
  }

  if (result.status === "failed") {
    return res.status(500).json({
      responseCode: 1,
      status: "error",
      errors: [
        {
          code: "INTERNAL_SERVER_ERROR",
          message: `Something went wrong `,
        },
      ],
    });
  }

  return res.status(200).json({
    responseCode: 0,
    status: "success",
    data: {
      message: "Cron job executed successfully",
      duration: result.duration,
    },
  });
});

export const getLatestCronStatus = catchAsync(async (req, res) => {
  const latest = await CronLog.findOne().sort({ createdAt: -1 });
  const isRunning = latest && !latest.endTime;

  return res.status(200).json({
    responseCode: 0,
    success: true,
    data: {
      loading: isRunning,
    },
  });
});

export const getAllCronStatus = catchAsync(async (req, res) => {
   
  const queryObj = { ...req.query };
  const excludedFields = ['page', 'sort', 'limit', 'fields'];
  excludedFields.forEach(el => delete queryObj[el]);

  // Basic filtering
  let query = CronLog.find(queryObj);

  // Sorting
  if (req.query.sort) {
    const sortBy = req.query.sort.split(',').join(' ');
    query = query.sort(sortBy);
  } else {
    query = query.sort('-createdAt'); // default sort
  }

  // Field limiting
  if (req.query.fields) {
    const fields = req.query.fields.split(',').join(' ');
    query = query.select(fields);
  } else {
    query = query.select('-__v'); // exclude __v by default
  }

  // Pagination
  const page = req.query.page * 1 || 1;
  const limit = req.query.limit * 1 || 100;
  const skip = (page - 1) * limit;
  query = query.skip(skip).limit(limit);

  // Execute query
  const doc = await query;
  const totalCount = await CronLog.countDocuments();

  const processedData = doc.map(record => {
    const recordObj = record.toObject();
    return {
      ...recordObj,
      totalProducts: recordObj.totalProducts || 0,
      totalSkus: recordObj.totalSkus || 0,
      updatedSkus: recordObj.updatedSkus || 0,
      skippedSkus: recordObj.skippedSkus || 0,
    };
  });

  return res.status(200).json({
    responseCode: 0,
    success: true,
    data: {
      data: processedData,
      totalCount,
    },
  });
});
