import { useAppBridge } from "@shopify/app-bridge-react";
import { useEffect, useState } from "react";

const useSearchProduct = (searchValue, searchParams = {}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState([]);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    const getProducts = async () => {
      try {
        setIsLoading(true);
        setHasError(false);
        setData([]);
        const response = await fetch("/api/order/fulfilled-orders", {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        });
      } catch (err) {
        setHasError(true);
      }
    };
  }, []);
};
