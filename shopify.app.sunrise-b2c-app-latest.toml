# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "24a245f51ad7ae90197d4cbe9ff7a2d2"
name = "Sunrise-b2c-app-latest"
handle = "sunrise-b2c-app-latest"
application_url = "https://packing-displayed-align-italian.trycloudflare.com"
embedded = true

[build]
automatically_update_urls_on_dev = true
dev_store_url = "shiva-internation.myshopify.com"
include_config_on_deploy = true

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "write_orders,read_fulfillments,read_inventory,read_locations,read_orders,write_customers,write_inventory,write_locations,write_products"

[auth]
redirect_urls = [
  "https://packing-displayed-align-italian.trycloudflare.com/auth/callback",
  "https://packing-displayed-align-italian.trycloudflare.com/auth/shopify/callback",
  "https://packing-displayed-align-italian.trycloudflare.com/api/auth/callback"
]

[webhooks]
api_version = "2025-01"

[app_proxy]
url = "https://packing-displayed-align-italian.trycloudflare.com"
subpath = "b2c"
prefix = "apps"

[pos]
embedded = false