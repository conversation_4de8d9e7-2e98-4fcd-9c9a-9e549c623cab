class NewAppError extends Error {
  constructor(code, message, statusCode) {
    super(message);
    this.code = code || "SERVER_ERROR";
    this.message = message || "Something went wrong.";
    this.statusCode = statusCode || 500;
    this.status = `${this.statusCode}`.startsWith("4") ? "fail" : "error";
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
  format() {
    return {
      responseCode: 1,
      status: this.status,
      errors: [
        {
          code: this.code,
          message: this.message,
        },
      ],
    };
  }
}

export const globalErrorHandler = (error, req, res, next) => {
  // If error is an instance of NewAppError, use its formatted response
  if (error instanceof NewAppError) {
    return res.status(error.statusCode).json(error.format());
  }
  // Default Internal Server Error
  return res.status(500).json({
    responseCode: 1,
    status: "error",
    errors: [
      {
        code: "SERVER_ERROR",
        message: "Something went wrong. Please try again later.",
      },
    ],
  });
};
export default NewAppError;
