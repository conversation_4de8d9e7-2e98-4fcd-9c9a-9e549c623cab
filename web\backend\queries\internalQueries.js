const GET_SHOPIFY_LOCATIONS = `{
    locations(first: 250) {
      edges {
        node {
          id
          name
         
        }
      }
    }
  }`;

const CREATE_LOCATION_SHOPIFY = `mutation MyMutation($input: LocationAddInput!) {
          locationAdd(input: $input) {
            location {
              id
            }
            userErrors {
              code
              field
              message
            }
          }
        }`;

const INVENTORY_SET_QUANTITIES = `mutation inventorySetQuantities($input: InventorySetQuantitiesInput!) {
        inventorySetQuantities(input: $input) {
          inventoryAdjustmentGroup {
      
            changes {
              name
              delta
              quantityAfterChange
            }
          }
          userErrors {
            code
            field
            message
          }
        }
      }`;

const ORDER_FULFILLED_WEBHOOK = `mutation OrderFulfilledWebhook($callbackUrl: URL) {
          webhookSubscriptionCreate(
            topic: ORDERS_FULFILLED
            webhookSubscription: {callbackUrl: $callbackUrl, format: JSON}
          ) {
            userErrors {
              field
              message
            }
            webhookSubscription {
              callbackUrl
              createdAt
              id
              topic
            }
          }
        }`;

const REFUNDS_CREATE_WEBHOOK = `mutation RefundsCreateWebhook($callbackUrl: URL) {
          webhookSubscriptionCreate(
            topic: REFUNDS_CREATE
            webhookSubscription: {callbackUrl: $callbackUrl, format: JSON}
          ) {
            userErrors {
              field
              message
            }
            webhookSubscription {
              callbackUrl
              createdAt
              id
              topic
            }
          }
        }`;

const FETCH_FULFILLED_ORDERS = `query FetchFulfilledOrders($cursor: String) {
    orders(first: 250, query: "(fulfillment_status:fulfilled OR fulfillment_status:partial)", after: $cursor) {
      edges {
        node {
          id
          name
          tags
          lineItems(first: 250) {
            edges {
              node {
                fulfillmentService {
                  location {
                    id
                  }
                }
                id
                name
                quantity
                fulfillmentStatus
                sku
              }
            }
          }
          updatedAt
        }
      }
      pageInfo {
        endCursor
        hasNextPage
      }
    }
  }`;

const CREATE_PRODUCT_WITH_VARIANTS = `
mutation MyMutation($input: ProductSetInput!) {
  productSet(input: $input) {
    product {
      id
      title
      variants(first: 10) {
        nodes {
          id
          price
          sku
          inventoryItem {
             id
          }
        }
      }
    }
    userErrors {
      code
      field
      message
    }
  }
}
`;

const CREATE_VARIANT = `
    mutation CreateProductVariants($productId: ID!, $variantsInput: [ProductVariantsBulkInput!]!) {
  productVariantsBulkCreate(productId: $productId, variants: $variantsInput) {
    productVariants {
      id
      title
      inventoryItem {
        id
      }
      selectedOptions {
        name
        value
      }
    }
    userErrors {
      field
      message
    }
  }
}
  `;

const INVENTORY_ACTIVATE = `
      mutation inventoryActivate($inventoryItemId: ID!, $locationId: ID!) {
        inventoryActivate(inventoryItemId: $inventoryItemId, locationId: $locationId) {
          inventoryLevel { id }
          userErrors { field message }
        }
 }`;

const UPDATE_VARIANT = `
mutation UpdateVariant($productId: ID!, $variantsInput: [ProductVariantsBulkInput!]!) {
      productVariantsBulkUpdate(productId: $productId, variants: $variantsInput) {
        productVariants {
          id
          title
          price
          inventoryItem {
             id
          }
          selectedOptions {
            name
            value
          }
        }
        userErrors {
          field
          message
        }
      }
    }
`;

const GET_VARIANT_DETAILS_BY_SKU = `
query getVariantInventory($query: String!) {
  productVariants(first: 250, query: $query) {
    nodes {
      id
      sku
      inventoryItem {
        id
        inventoryLevels(first: 10) {
          edges {
            cursor
            node {
              id
              location {
                id
                name
              }
              quantities(names: ["available", "on_hand"]) { 
                id
                name
                quantity
              }
            }
          }
        }
      }
    }
  }
}

`;

export {
  GET_SHOPIFY_LOCATIONS,
  CREATE_LOCATION_SHOPIFY,
  INVENTORY_SET_QUANTITIES,
  ORDER_FULFILLED_WEBHOOK,
  REFUNDS_CREATE_WEBHOOK,
  FETCH_FULFILLED_ORDERS,
  CREATE_PRODUCT_WITH_VARIANTS,
  CREATE_VARIANT,
  INVENTORY_ACTIVATE,
  UPDATE_VARIANT,
  GET_VARIANT_DETAILS_BY_SKU,
};
