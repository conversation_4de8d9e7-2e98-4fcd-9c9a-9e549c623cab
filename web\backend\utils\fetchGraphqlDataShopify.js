import axios from "axios";

export default async function fetchGraphqlDataShopify(
  query,
  variables,
  API_VERSION
) {
  try {
    let data = JSON.stringify({
      query: query,
      variables: variables,
    });

    let config = {
      method: "post",
      maxBodyLength: Infinity,
      url: `${process.env.SHOP_URL}/admin/api/${
        API_VERSION || process.env.VERSION
      }/graphql.json`,
      headers: {
        "X-Shopify-Access-Token": process.env.SHOPIFY_ADMIN_ACCESS_TOKEN,
        "Content-Type": "application/json",
      },
      data: data,
    };

    const response = await axios.request(config);
    return response.data;
  } catch (error) {
    console.log(error);
    return { error };
  }
}
