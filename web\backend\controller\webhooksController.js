import { createFulFillmentService } from "../service/webhooks.js/fulfillmentCreateService.js";
import { handleRefundCreate as handleRefundCreateService } from "../service/webhooks.js/refundCreateService.js";
import { sendSuccess } from "../utils/successHandler.js";

const handleFulfilmentCreate = async (req, res) => {
  try {
    sendSuccess(res, {}, "Fulfillment created");
    const payload = req.body;
    if (!payload) {
      return;
    }
    if (payload.tags && payload.tags.includes("migrated")) {
      console.log("Skipping fulfillment creation for migrated tag");
      return;
    }
    await createFulFillmentService(payload);
  } catch (err) {
    console.log(" Error occured in the fulfillement creation", err);
  }
};

const handleRefundCreate = async (req, res) => {
  try {
    sendSuccess(res, {}, "Refund created");
    const payload = req.body;
    if (!payload) {
      return;
    }
    await handleRefundCreateService(payload);
  } catch (err) {
    console.log("Error occurred in the refund creation", err);
  }
};

export { handleFulfilmentCreate, handleRefundCreate };
