import {
  GET_VARIANT_DETAILS_BY_SKU,
  INVENTORY_SET_QUANTITIES,
} from "../../queries/internalQueries.js";
import fetchGraphqlDataShopify from "../../utils/fetchGraphqlDataShopify.js";
import { LOCATION, B2BorB2C } from "../../Constants/erpConstants.js";
import axios from "axios";

const chunkArray = (arr, size) => {
  const chunks = [];
  for (let i = 0; i < arr.length; i += size) {
    chunks.push(arr.slice(i, i + size));
  }
  return chunks;
};

export const fetchERPItemsBatch = async (page = 1, pageSize = 50000) => {
  try {
    const url = `${process.env.SAGE_CUSTOM_API_ENDPOINT}/api/ICItem/GetICItems`;
    const params = {
      company: process.env.SAGE_COMPANY,
      APIKey: process.env.SAGE_CUSTOM_API_KEY,
      page_no: page,
      page_size: pageSize,
      Location: LOCATION,
      OptionalFields: "BUDSUBGRP,COORIGIN",
      B2BorB2C: B2BorB2C,
      Active: "True",
    };

    const headers = {
      accept: "*/*",
    };

    const response = await axios.get(url, { params, headers });
    console.log(response?.data?.length, "total products");
    return response.data || [];
  } catch (error) {
    console.error(`Error fetching ERP items on page ${page}:`, error.message);
    return [];
  }
};

const fetchVariantsBatch = async (skus = []) => {
  const queryString = skus.map((sku) => `sku:${sku}`).join(" OR ");
  const response = await fetchGraphqlDataShopify(GET_VARIANT_DETAILS_BY_SKU, {
    query: queryString,
  });

  const variants = response?.data?.productVariants?.nodes || [];
  const variantMap = {};

  for (const variant of variants) {
    const inventoryItem = variant.inventoryItem;
    if (!inventoryItem) continue;

    const levels = inventoryItem.inventoryLevels?.edges || [];

    const matchedLevel = levels.find(
      (edge) => edge?.node?.location?.name === LOCATION
    );

    if (!matchedLevel) {
      console.log(
        `No inventory level found for SKU ${variant.sku} at location ${LOCATION}`
      );
      continue;
    }

    variantMap[variant.sku] = {
      variantId: variant.id,
      inventoryItemId: inventoryItem.id,
      location: matchedLevel.node.location,
      quantities: matchedLevel.node.quantities,
    };
  }

  return variantMap;
};

const extractLocationInfo = (item) => {
  const locationData = [];

  // Extract all unique location codes based on keys ending with '_QuantityOnHand'
  const locationCodes = Object.keys(item)
    .filter((key) => key.endsWith("_QuantityOnHand"))
    .map((key) => key.split("_")[0]);

  for (const location of locationCodes) {
    let quantityOnHand = item[`${location}_QuantityOnHand`];
    let quantityAvailable = item[`${location}_QuantityAvailable`];

    quantityOnHand =
      quantityOnHand != null && quantityOnHand >= 0 ? quantityOnHand : 0;
    quantityAvailable =
      quantityAvailable != null && quantityAvailable >= 0
        ? quantityAvailable
        : 0;

    locationData.push({
      location,
      quantityOnHand,
      quantityAvailable,
    });
  }
  return locationData;
};

export const setInventoryLevelsBulk = async (updates = []) => {
  if (updates.length === 0) return;

  const quantities = updates.map((update) => ({
    inventoryItemId: update.inventoryItemId,
    locationId: update.locationId,
    quantity: update.quantity,
  }));

  const response = await fetchGraphqlDataShopify(INVENTORY_SET_QUANTITIES, {
    input: {
      name: "available",
      reason: "correction",
      ignoreCompareQuantity: true,
      quantities,
    },
  });

  const errors = response.data?.inventorySetQuantities?.userErrors || [];
  if (errors.length > 0) {
    throw new Error(errors.map((e) => e.message).join(", "));
  }

  return true;
};

export const updateInventoryLevels = async () => {
  try {
    let page = 1;
    const pageSize = 50000;
    let totalUpdated = 0;
    let totalSkipped = 0;
    let batchCount = 0;
    let totalSkus = 0;

    while (true) {
      const items = await fetchERPItemsBatch(page, pageSize);
      // items = items.filter((x) => x.ItemNumber.includes("BAG22926TFEZ"));
      if (!items || items.length === 0) break;

      const skus = items.map((item) => item.UnformattedItemNumber);
      totalSkus += skus.length;

      const skuChunks = chunkArray(skus, 250);
      for (let i = 0; i < skuChunks.length; i++) {
        const chunk = skuChunks[i];
        const variantMap = await fetchVariantsBatch(chunk);

        // Only include items for this chunk
        const chunkItems = items.filter((item) =>
          chunk.includes(item.UnformattedItemNumber)
        );

        const inventoryUpdates = [];
        let updatedCount = 0;
        let skippedCount = 0;

        for (const item of chunkItems) {
          const sku = item.UnformattedItemNumber;
          const variant = variantMap[sku];
          if (!variant || !variant.location || !variant.inventoryItemId) {
            skippedCount++;
            continue;
          }

          const shopifyLocation = variant.location;
          const available = variant.quantities?.find(
            (q) => q.name === "available"
          )?.quantity;
          const onHand = variant.quantities?.find(
            (q) => q.name === "on_hand"
          )?.quantity;

          const erpLocation = extractLocationInfo(item)[0];
          const isSame =
            erpLocation.quantityAvailable === available &&
            erpLocation.quantityOnHand === onHand;

          if (isSame) {
            skippedCount++;
            continue;
          }

          inventoryUpdates.push({
            inventoryItemId: variant.inventoryItemId,
            locationId: shopifyLocation.id,
            quantity: erpLocation.quantityAvailable,
          });
          updatedCount++;
        }

        if (inventoryUpdates.length > 0) {
          await setInventoryLevelsBulk(inventoryUpdates);
        }

        totalUpdated += updatedCount;
        totalSkipped += skippedCount;
        batchCount++;

        console.log(
          `✅ Batch ${batchCount} completed - ${chunk.length} SKUs processed | Updated: ${updatedCount}, Skipped: ${skippedCount}`
        );
      }

      if (items.length < pageSize) break;
      page++;
    }

    console.log("🎯 All batches completed.");
    console.log(`🔢 Total SKUs: ${totalSkus}`);
    console.log(`✅ Total Updated: ${totalUpdated}`);
    console.log(`⏭️ Total Skipped: ${totalSkipped}`);
    console.log(`📦 Total Batches: ${batchCount}`);

    return {
      totalSkus,
      updatedSkus: totalUpdated,
      skippedSkus: totalSkipped,
      batchCount,
      totalUpdated,
      totalSkipped,
    };
  } catch (error) {
    console.log(error);
  }
};
