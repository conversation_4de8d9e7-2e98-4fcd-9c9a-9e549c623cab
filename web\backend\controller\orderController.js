import Order from "../model/order.js";
import { getATFromSQL } from "../utils/tokenHelper.js";
import { getALLOrdersService } from "../service/order/getOrdersService.js";
import { catchAsync } from "../utils/helpers.js";
import NewAppError from "../utils/errorHandlers.js";
import { sendSuccess } from "../utils/successHandler.js";
import fetchGraphqlDataShopify from "../utils/fetchGraphqlDataShopify.js";
import { FETCH_FULFILLED_ORDERS } from "../queries/internalQueries.js";

export const getAllOrders = catchAsync(async (req, res) => {
  const orders = await getALLOrdersService(req);
  sendSuccess(res, { data: orders });
});


export const getDataFromDB = async (req, res, next) => {
  try {
    const { search = "", page = 1, limit = 25 } = req.query;

    const pageNumber = parseInt(page);
    const pageSize = parseInt(limit);

    const query = {
      $or: [
        { orderName: { $regex: search, $options: "i" } },
        { sku: { $regex: search, $options: "i" } },
        { title: { $regex: search, $options: "i" } },
      ],
    };

    const totalOrders = await Order.countDocuments(query);
    const orders = await Order.find(query)
      .skip((pageNumber - 1) * pageSize)
      .limit(pageSize);

    const hasNext = pageNumber * pageSize < totalOrders;

    sendSuccess(res, {
      data: {
        data: orders,
        pagination: {
          page: pageNumber,
          limit: pageSize,
          totalPages: Math.ceil(totalOrders / pageSize),
          totalItems: totalOrders,
          hasNext,
        },
      },
    });
  } catch (error) {
    console.error("Error fetching orders:", error);
    return next(
      new NewAppError(
        "SERVER_ERROR",
        "Error while creating orders on ERP.",
        500
      )
    );
  }
};

export const undoOrder = async (req, res, next) => {
  try {
    let { orderId } = req.query;
    if (!orderId) {
      return next(new NewAppError("SERVER_ERROR", "orderId is required", 400));
    }

    const update = {
      status: "PENDING",
      orderNumber: "",
    };

    try {
      await Order.updateOne(
        { orderId },
        {
          $set: update,
          $unset: { erpIdentifier: "" },
        }
      );
      sendSuccess(res, {}, "Order successfully undone.");
    } catch (err) {
      await Order.updateOne(
        { orderId },
        {
          $set: {
            ...update,
            erpIdentifier: -1,
          },
        }
      );
      return next(
        new NewAppError(
          "SERVER_ERROR",
          "Error while creating orders on ERP.",
          500
        )
      );
    }
  } catch (error) {
    console.error("Error undoing order:", error);
    return next(
      new NewAppError("SERVER_ERROR", "Error while undoing error", 500)
    );
  }
};
