import sqlite3 from "sqlite3";

const getATFromSQL = async () => {
  return new Promise((resolve, reject) => {
    const db = new sqlite3.Database("./database.sqlite");
    db.all(`SELECT shop, accessToken FROM shopify_sessions`, (err, rows) => {
      if (err) {
        reject(err);
      } else if (rows) {
        resolve(rows);
      } else {
        resolve(null);
      }
    });
    db.close();
  });
};

export { getATFromSQL };
