import mongoose from "mongoose";

const ProductErrorSchema = new mongoose.Schema(
  {
    segment1: {
      type: String,

    },
    sku: {
      type: String,

    },
    event: {
      type: String,
    },
    data:{
      type: Map,
      of: mongoose.Schema.Types.Mixed, 
    },
    errorMessage: {
      type: String,
      required: true,
    },
  },
  { timestamps: true }
);

const ProductErrorLog = mongoose.model("ProductErrorLog", ProductErrorSchema);
export default ProductErrorLog;