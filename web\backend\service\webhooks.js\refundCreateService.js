import Order from "../../model/order.js";
import { creditDebitNoteUpdate } from "../sageOrders/sageOrderService.js";

export const updateRefundedFlags = async (orderId) => {
  try {
    const order = await Order.findOne({ orderId: orderId });
    if (!order) {
      console.log(`Order not found for orderId: ${orderId}`);
      return;
    }

    const refundedItemIds = order.refunds.map((refund) =>
      refund.line_item_id.toString()
    );

    order.lineItems = order.lineItems.map((item) => ({
      ...item,
      refunded: refundedItemIds.includes(item.id.toString()),
    }));

    // Check if all line items are refunded
    const allItemsRefunded = order.lineItems.every((item) => item.refunded);

    // Update order status based on refund status
    if (allItemsRefunded) {
      order.paymentStatus = "refunded";
    } else if (refundedItemIds.length > 0) {
      order.paymentStatus = "partially_refunded";
    }

    await order.save();

    console.log(`Updated refunded flags and status for order ${orderId}`);
  } catch (err) {
    console.error("Error updating refunded flags:", err);
  }
};

export const handleRefundCreate = async (payload) => {
  try {
    if (!payload || !payload.order_id) {
      console.log("Invalid refund payload");
      return;
    }

    const order = await Order.findOne({ orderId: payload.order_id.toString() });
    if (!order) {
      console.log(`Order not found for orderId: ${payload.order_id}`);
      return;
    }

    // Simplified refund items with id, quantity and line_item_id
    const refundItems = payload.refund_line_items.map((item) => ({
      id: item.id.toString(), // Adding the required id field
      line_item_id: item.line_item_id.toString(),
      quantity: item.quantity,
    }));

    // Add simplified refund to refunds array
    order.refunds.push(...refundItems);
    await order.save();

    // Update the refunded flags
    await updateRefundedFlags(payload.order_id);
    
    if (order.status === "PUSHED") {
      await creditDebitNoteUpdate(payload);
    }
  } catch (err) {
    console.log("Error handling refund webhook:", err);
  }
};
