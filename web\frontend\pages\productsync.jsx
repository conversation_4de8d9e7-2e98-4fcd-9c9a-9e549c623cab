"use client";

import { useCallback, useEffect, useState } from "react";
import axios from "axios";
import {
  <PERSON>,
  Card,
  But<PERSON>,
  Spinner,
  Text,
  DataTable,
  Badge,
} from "@shopify/polaris";

export default function ProductSyncWrapper() {
  const [logsData, setLogsData] = useState([]);
  const [fetching, setFetching] = useState(false);
  const [page, setPage] = useState(1);
  const [refreshing, setRefreshing] = useState(false);
  const [syncing, setSyncing] = useState(false);

  const limit = 50;

  useEffect(() => {
    getAllLogs(page);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page]);

  const getAllLogs = async (page = 1) => {
    setFetching(true);
    try {
      const res = await axios.get(
        `/api/cron/all?page=${page}&limit=${limit}`
      );
      if (res.data && res.data.data) {
        setLogsData(res.data.data.data || []);
      }
    } catch (err) {
      console.error("Error fetching logs:", err);
    } finally {
      setFetching(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    getAllLogs(page);
  };

  const handleSyncProducts = async () => {
    setSyncing(true);
    try {
      await axios.post(`/api/cron`);
      getAllLogs(page);
    } catch (error) {
      console.error("Error syncing products:", error);
    } finally {
      setSyncing(false);
    }
  };

  const isCronRunning = logsData.some(
    (log) => log.status?.trim().toUpperCase() === "RUNNING"
  );

  const getBadgeStatus = (status) => {
    const clean = status?.trim().toLowerCase();
    if (clean === "success") return "success";
    if (clean === "failed") return "critical";
    if (clean === "running") return "warning";
    return "info";
  };

  const rows =
    logsData.length > 0
      ? logsData.map((log) => [
          new Date(log.createdAt).toLocaleDateString("en-SG", { timeZone: "Asia/Singapore" }) || "-",
          log.startTime ? new Date(log.startTime).toLocaleTimeString("en-SG", { timeZone: "Asia/Singapore" }) : "-",
          log.endTime ? new Date(log.endTime).toLocaleTimeString("en-SG", { timeZone: "Asia/Singapore" }) : "-",
          log.triggeredBy || "-",
          <Badge tone={getBadgeStatus(log.status)}>{log.status || "-"}</Badge>,
          log.duration ? Math.round(log.duration / 60000) : "-", // Duration in minutes
          log.updatedSkus ?? "-", // Processed/Updated SKUs
          log.totalSkus ?? "-", // Total SKUs
          log.skippedSkus ?? "-", // Skipped SKUs
          log.updatedAt ? new Date(log.updatedAt).toLocaleTimeString("en-SG", { timeZone: "Asia/Singapore" }) : "-",
        ])
      : [];

  return (
    <Page fullWidth>
      <div
        style={{
          width: "100vw",
          maxWidth: "100vw",
          marginLeft: "calc(-50vw + 50%)",
          padding: 24,
          boxSizing: "border-box",
        }}
      >
        <Card sectioned>
          <div
            style={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Text variant="headingLg" as="h2">
              Product Sync
            </Text>
            <div style={{ display: "flex", gap: 8 }}>
              <Button onClick={handleRefresh} loading={refreshing}>
                Refresh List
              </Button>
              <Button
                primary
                disabled={syncing || isCronRunning}
                loading={syncing}
                onClick={handleSyncProducts}
              >
                {isCronRunning ? "Cron Running..." : "Sync Products Manually"}
              </Button>
            </div>
          </div>
        </Card>

        <Card sectioned>
          {fetching ? (
            <div
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                minHeight: 120,
              }}
            >
              <Spinner accessibilityLabel="Loading logs" size="large" />
            </div>
          ) : logsData.length === 0 ? (
            <Text variant="bodyMd" as="p" alignment="center">
              No logs found.
            </Text>
          ) : (
            <div style={{ width: "100%", overflowX: "auto" }}>
              <style>{`
                .Polaris-DataTable__Table thead tr {
                  background:rgb(234, 234, 236) !important;
                }
              `}</style>
              <DataTable
                className="custom-sync-table"
                columnContentTypes={[
                  "text",
                  "text",
                  "text",
                  "text",
                  "text",
                  "numeric",
                  "numeric",
                  "numeric",
                  "numeric",
                  "text",
                  "text",
                ]}
                headings={[
                  "Date",
                  "Start Time",
                  "End Time",
                  "Triggered By",
                  "Status",
                  "Duration (min)",
                  <div key="updated-sku-head" style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end' }}>
                    <span>Updated Sku</span>
                    <span style={{ fontWeight: 400, fontSize: 12, color: '#888', lineHeight: 1, marginTop: 2 }}>(In shopify)</span>
                  </div>,
                  "Total Sku",
                  "Skipped Sku",
                  "",
                  "Last Sync",
                ]}
                rows={rows.map((r) => {
                  // Insert an empty cell for gap before Last Sync
                  const newRow = [...r];
                  newRow.splice(9, 0, "");
                  return newRow;
                })}
                showTotalsInFooter={false}
              />
            </div>
          )}
        </Card>
      </div>
    </Page>
  );
}
