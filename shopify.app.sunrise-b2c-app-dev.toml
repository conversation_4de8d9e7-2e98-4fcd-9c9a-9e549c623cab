# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "38dceaa5835e80c3cdfc672ec2959517"
name = "sunrise-b2c-app-dev"
handle = "sunrise-b2c-app-dev-1"
application_url = "https://lonely-backing-parental-velvet.trycloudflare.com"
embedded = true

[build]
automatically_update_urls_on_dev = true
dev_store_url = "shiva-internation.myshopify.com"
include_config_on_deploy = true

[webhooks]
api_version = "2025-04"

[access_scopes]
# Learn more at https://shopify.dev/docs/apps/tools/cli/configuration#access_scopes
scopes = "write_orders,read_fulfillments,read_inventory,read_locations,read_orders,write_customers,write_inventory,write_locations,write_products"

[auth]
redirect_urls = [
  "https://lonely-backing-parental-velvet.trycloudflare.com/auth/callback",
  "https://lonely-backing-parental-velvet.trycloudflare.com/auth/shopify/callback",
  "https://lonely-backing-parental-velvet.trycloudflare.com/api/auth/callback"
]

[pos]
embedded = false
